﻿using Amazon.IdentityManagement;
using Microsoft.Extensions.Caching.Memory;
using platform.Components.AWS;
using platform.Services;
using shared.Extension;

namespace platform.Components.Core
{
    public static class Account
    {
        public static async Task<bool> SetupAccount(IIAM iIAM, string accountId)
        {
            var response = await iIAM.CreatePolicy(
                $"ricca-s3-account-{accountId}",
                "Resources/AWS/Policies/s3_policy_0001.json"
            );

            response = await iIAM.CreatePolicy(
                $"ricca-s3-account-{accountId}",
                "Resources/AWS/Policies/bedrock_policy_0001.json"
            );

            return true;
        }
    }
}
