﻿
namespace shared.Types
{
    public class DynamicPrompt
    {
        public Dictionary<string, PriorityValue<string>> Prompts { get; set; } = new Dictionary<string, PriorityValue<string>>();

        public void PutPrompt(PromptEntry promptEntry)
        {
            if (Prompts.ContainsKey(promptEntry.PromptKey))
            {
                Prompts[promptEntry.PromptKey].Set(promptEntry.Prompt);
            }
            else
            {
                Prompts[promptEntry.PromptKey] = promptEntry.Prompt;
            }
        }
    }
}
