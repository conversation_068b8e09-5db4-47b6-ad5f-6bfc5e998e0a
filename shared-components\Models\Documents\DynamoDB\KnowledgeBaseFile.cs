using Amazon.DynamoDBv2.DataModel;
using shared.Models.Enums;
using shared.Converters;

using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(KnowledgeBaseFile))]
    public class KnowledgeBaseFile : DynamoDBModel
    {
        public const string AccountIdFileIdIndexName = "AccountId-FileId-index";

        [DynamoDBHashKey]
        public string KbId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdFileIdIndexName)]
        public string FileId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdFileIdIndexName)]
        public string AccountId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long UploadedFileSize { get; set; }
        public bool IsDeployed { get; set; } = false;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<KnowledgeBaseFileUploadStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseFileUploadStatus>))]
        public KnowledgeBaseFileUploadStatus Status { get; set; }

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(KnowledgeBaseFile.KbId);
            }
            else if (indexName == AccountIdFileIdIndexName)
            {
                // AccountId-FileId-index hash key
                return nameof(KnowledgeBaseFile.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(KnowledgeBaseFile.FileId);
            }
            else if (indexName == AccountIdFileIdIndexName)
            {
                // AccountId-FileId-index range key
                return nameof(KnowledgeBaseFile.FileId);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(KnowledgeBaseFile);
        }
    }
}
