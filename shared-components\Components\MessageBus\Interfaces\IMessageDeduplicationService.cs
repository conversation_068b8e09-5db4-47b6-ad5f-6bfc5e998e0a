namespace shared.Components.MessageBus.Interfaces
{
    /// <summary>
    /// Service for message deduplication using IMemoryCache with automatic expiration
    /// </summary>
    public interface IMessageDeduplicationService
    {
        /// <summary>
        /// Check if a message should be processed and mark it as processing if not a duplicate
        /// </summary>
        /// <param name="messageId">Message identifier</param>
        /// <returns>True if message should be processed, false if it's a duplicate</returns>
        bool ShouldProcessMessage(string messageId);

        /// <summary>
        /// Mark a message as requeued (removes from cache to allow reprocessing)
        /// </summary>
        /// <param name="messageId">Message identifier</param>
        void MarkMessageRequeued(string messageId);

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Dictionary containing cache statistics</returns>
        Dictionary<string, object> GetStatistics();
    }
}
