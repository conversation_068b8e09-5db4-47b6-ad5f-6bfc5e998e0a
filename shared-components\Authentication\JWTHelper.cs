﻿using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using shared.Models.Configuration;
using shared.Services;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace shared.Authentication
{
    public class JWTHelper
    {
        public static async Task<string> GenerateJwtMicroService(ISecretsService secretsService, MicroserviceConfiguration microserviceConfiguration, List<Claim> claims, int expireSeconds = 1440)
        {
            string jwtKey = await secretsService.Get(
                microserviceConfiguration.Environment,                           
                Constants.Authentication.MicroserviceAuthenticationKeySecretsName
            ) ?? string.Empty;

            return GenerateJwt(jwtKey, secretsService, claims, expireSeconds);
        }

        public static async Task<string> GenerateJwtUser(ISecretsService secretsService, MicroserviceConfiguration microserviceConfiguration, List<Claim> claims, int expireSeconds = 1440)
        {
            string jwtKey = await secretsService.Get(
                microserviceConfiguration.Environment,
                Constants.Authentication.UserAuthenticationKeySecretsName
            ) ?? string.Empty;

            return GenerateJwt(jwtKey, secretsService, claims, expireSeconds);
        }

        public static async Task<string> GenerateJwtAgent(ISecretsService secretsService, MicroserviceConfiguration microserviceConfiguration, List<Claim> claims, int expireSeconds = 1440)
        {
            string jwtKey = await secretsService.Get(
                microserviceConfiguration.Environment,
                Constants.Authentication.AgentAuthenticationKeySecretsName
            ) ?? string.Empty;

            return GenerateJwt(jwtKey, secretsService, claims, expireSeconds);
        }

        public static string GenerateJwt(string key, ISecretsService secretsService, List<Claim> claims, int expireSeconds)
        {
            var jwtToken = new JwtSecurityToken(
                claims: claims,
                notBefore: DateTime.UtcNow,
                expires: DateTime.UtcNow.AddSeconds(expireSeconds),
                signingCredentials: new SigningCredentials(
                    new SymmetricSecurityKey(
                       Encoding.UTF8.GetBytes(key)
                    ),
                    SecurityAlgorithms.HmacSha256Signature)
                );
            return new JwtSecurityTokenHandler().WriteToken(jwtToken);
        }
    }
}
