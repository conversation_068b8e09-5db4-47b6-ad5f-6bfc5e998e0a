using System.Text.Json.Serialization;
using shared.Converters;

namespace shared.Components.MultiStepProcess.Enums
{
    /// <summary>
    /// Represents the execution state of a multi-step process.
    /// Controls whether the process is executing forward (StateTask) or backward (RollbackTask).
    /// </summary>
    [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessState>))]
    public enum MultiStepProcessState
    {
        /// <summary>
        /// Process is executing forward through states, running StateTask for each step.
        /// </summary>
        Running,

        /// <summary>
        /// Process is rolling back through states in reverse order, running RollbackTask for each step.
        /// </summary>
        RollingBack
    }
}
