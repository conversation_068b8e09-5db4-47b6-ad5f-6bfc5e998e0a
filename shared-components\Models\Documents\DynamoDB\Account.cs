using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(Account))]
    public class Account : DynamoDBModel
    {

        [DynamoDBHashKey]
        public string Id { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(Account.Id);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table has no range key
                return null;
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(Account);
        }
    }
}
