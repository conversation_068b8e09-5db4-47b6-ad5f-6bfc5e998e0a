using Amazon.DynamoDBv2.DataModel;
using shared.Components.FlowBuilder.Models;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.FlowBuilder.Models
{
        /// <summary>
        /// Core flow definition model.
        /// </summary>
        public class Flow
        {
            public string Id { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Version { get; set; } = "1.0.0";
            public List<FlowNode> Nodes { get; set; } = new();
            public List<FlowEdge> Edges { get; set; } = new();
            public List<FlowField> Fields { get; set; } = new();
            public Dictionary<string, object> Metadata { get; set; } = new();
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

            /// <summary>
            /// Gets a node by its ID.
            /// </summary>
            public FlowNode? GetNode(string nodeId)
            {
                return Nodes.FirstOrDefault(n => n.Id == nodeId);
            }

            /// <summary>
            /// Gets all edges connected to a node.
            /// </summary>
            public List<FlowEdge> GetNodeEdges(string nodeId)
            {
                return Edges.Where(e => e.Source == nodeId || e.Target == nodeId).ToList();
            }

            /// <summary>
            /// Gets the starting nodes (nodes with no incoming edges).
            /// </summary>
            public List<FlowNode> GetStartingNodes()
            {
                var targetNodeIds = Edges.Select(e => e.Target).ToHashSet();
                return Nodes.Where(n => !targetNodeIds.Contains(n.Id)).ToList();
            }

            /// <summary>
            /// Gets fields by type.
            /// </summary>
            public List<FlowField> GetFieldsByType(FlowFieldType type)
            {
                return Fields.Where(f => f.Type == type).ToList();
            }
        }

        /// <summary>
        /// Flow node definition.
        /// </summary>
        public class FlowNode
        {
            public string Id { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public FlowPosition Position { get; set; } = new();
            public FlowNodeData Data { get; set; } = new();
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// Flow node data containing configuration.
        /// </summary>
        public class FlowNodeData
        {
            public FlowBaseNodeData BaseData { get; set; } = new();
            public Dictionary<string, object> NodeData { get; set; } = new();
            public FlowNodeEvents? Events { get; set; }
        }

        /// <summary>
        /// Base node data structure.
        /// </summary>
        public class FlowBaseNodeData
        {
            public string Name { get; set; } = string.Empty;
            public object? NodeData { get; set; }
            public FlowRuleGroup? NodeConditions { get; set; }
        }

        /// <summary>
        /// Node events configuration.
        /// </summary>
        public class FlowNodeEvents
        {
            public string? OnOpenDrawer { get; set; }
            public Dictionary<string, object> CustomEvents { get; set; } = new();
        }

        /// <summary>
        /// Flow edge definition.
        /// </summary>
        public class FlowEdge
        {
            public string Id { get; set; } = string.Empty;
            public string Source { get; set; } = string.Empty;
            public string Target { get; set; } = string.Empty;
            public string? SourceHandle { get; set; }
            public string? TargetHandle { get; set; }
            public bool Animated { get; set; } = false;
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// Flow field definition.
        /// </summary>
        public class FlowField
        {
            public string Id { get; set; } = string.Empty;
            public FlowFieldType Type { get; set; }
            public string Name { get; set; } = string.Empty;
            public FlowDataType DataType { get; set; }
            public bool Required { get; set; } = false;
            public string DefaultValue { get; set; } = string.Empty;
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// Flow field types.
        /// </summary>
        public enum FlowFieldType
        {
            Variable,
            Input
        }

        /// <summary>
        /// Flow data types.
        /// </summary>
        public enum FlowDataType
        {
            STRING,
            INTEGER,
            FLOAT,
            BOOLEAN,
            JSON
        }

        /// <summary>
        /// Position information for nodes.
        /// </summary>
        public class FlowPosition
        {
            public double X { get; set; } = 0;
            public double Y { get; set; } = 0;
        }

        /// <summary>
        /// Rule group for conditional logic (similar to query builder).
        /// </summary>
        public class FlowRuleGroup
        {
            public string Combinator { get; set; } = "and"; // "and" or "or"
            public List<FlowRule> Rules { get; set; } = new();
            public List<FlowRuleGroup> Groups { get; set; } = new();
        }

        /// <summary>
        /// Individual rule for conditional logic.
        /// </summary>
        public class FlowRule
        {
            public string Field { get; set; } = string.Empty;
            public string Operator { get; set; } = string.Empty; // "=", "!=", "<", ">", "<=", ">=", "contains", etc.
            public object? Value { get; set; }
        }
}
