using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Base interface for all flow tasks. Tasks are actual execution units that perform work
    /// and can affect how the flow executes. Tasks run within TaskNodes.
    /// </summary>
    public interface IFlowTask
    {
        /// <summary>
        /// Gets the unique identifier for this task instance.
        /// </summary>
        string TaskId { get; }

        /// <summary>
        /// Gets the type of this task (e.g., "DummyTask", "ConditionalTask", "LambdaTask", etc.).
        /// </summary>
        string TaskType { get; }

        /// <summary>
        /// Gets the display name for this task.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the task definition that contains configuration data.
        /// </summary>
        FlowTaskDefinition Definition { get; }

        /// <summary>
        /// Indicates whether this task supports long-running operations that should use the event bus.
        /// </summary>
        bool IsLongRunning { get; }

        /// <summary>
        /// Indicates whether this task can stream intermediate results during execution.
        /// </summary>
        bool SupportsStreaming { get; }

        /// <summary>
        /// Initializes the task with its definition and validates configuration.
        /// </summary>
        /// <param name="definition">The task definition containing configuration</param>
        /// <returns>True if initialization was successful</returns>
        Task<bool> InitializeAsync(FlowTaskDefinition definition);

        /// <summary>
        /// Validates the task configuration.
        /// </summary>
        /// <param name="context">Validation context with flow information</param>
        /// <returns>Validation result</returns>
        Task<TaskValidationResult> ValidateAsync(FlowValidationContext context);

        /// <summary>
        /// Executes the task asynchronously for short-running operations.
        /// </summary>
        /// <param name="context">Execution context with state and field access</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task execution result</returns>
        Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts a long-running task execution using the event bus system.
        /// This method should return quickly and use events for completion notification.
        /// </summary>
        /// <param name="context">Execution context with state and field access</param>
        /// <returns>Execution session ID for tracking</returns>
        Task<string> StartLongRunningExecutionAsync(FlowExecutionContext context);

        /// <summary>
        /// Gets the current status of a long-running task execution.
        /// </summary>
        /// <param name="executionSessionId">The execution session ID</param>
        /// <returns>Current execution status</returns>
        Task<TaskExecutionStatus> GetExecutionStatusAsync(string executionSessionId);

        /// <summary>
        /// Cancels a running task execution.
        /// </summary>
        /// <param name="executionSessionId">The execution session ID to cancel</param>
        /// <returns>True if cancellation was successful</returns>
        Task<bool> CancelExecutionAsync(string executionSessionId);

        /// <summary>
        /// Called when the task execution is completed (successfully or with error).
        /// </summary>
        /// <param name="result">The execution result</param>
        /// <param name="context">Execution context</param>
        Task OnExecutionCompletedAsync(TaskExecutionResult result, FlowExecutionContext context);

        /// <summary>
        /// Called when the task execution is cancelled.
        /// </summary>
        /// <param name="context">Execution context</param>
        Task OnExecutionCancelledAsync(FlowExecutionContext context);

        /// <summary>
        /// Event fired when the task wants to stream intermediate results.
        /// </summary>
        event EventHandler<TaskStreamEventArgs>? OnStreamResult;

        /// <summary>
        /// Event fired when the task status changes (for long-running tasks).
        /// </summary>
        event EventHandler<TaskStatusChangedEventArgs>? OnStatusChanged;
    }

    /// <summary>
    /// Event arguments for task streaming results.
    /// </summary>
    public class TaskStreamEventArgs : EventArgs
    {
        public string TaskId { get; set; } = string.Empty;
        public string ExecutionSessionId { get; set; } = string.Empty;
        public string StreamType { get; set; } = string.Empty; // e.g., "progress", "message", "data"
        public object Data { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for task status changes.
    /// </summary>
    public class TaskStatusChangedEventArgs : EventArgs
    {
        public string TaskId { get; set; } = string.Empty;
        public string ExecutionSessionId { get; set; } = string.Empty;
        public TaskExecutionStatus OldStatus { get; set; }
        public TaskExecutionStatus NewStatus { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
