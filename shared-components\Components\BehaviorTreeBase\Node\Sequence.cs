﻿using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;

namespace shared.Components.BehaviorTree.Node
{
    public class Sequence : Node
    {
        public Sequence(string nodeId, List<Node> children, ConditionExpression? conditionExpression) : base(nodeId, children, conditionExpression) { }

        protected override async Task<NodeState> EvaluateImpl(TreeState treeData)
        {
            bool anyChildIsRunning = false;
            NodeState state;

            foreach (Node node in Children)
            {
                NodeState childState = await node.Evaluate(treeData);
                switch (childState)
                {
                    case NodeState.FAILURE:
                        state = NodeState.FAILURE;
                        return state;
                    case NodeState.SUCCESS:
                        continue;
                    case NodeState.RUNNING:
                        anyChildIsRunning = true;
                        continue;
                    default:
                        state = NodeState.SUCCESS;
                        return state;
                }
            }

            state = anyChildIsRunning ? NodeState.RUNNING : NodeState.SUCCESS;
            return state;
        }
    }
}
