using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.Interfaces;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Select Node - Chooses between multiple execution paths, selecting the first successful one.
    /// Has 1 input (input handle on left) and configurable outputs (default 2, can be increased).
    /// </summary>
    public class SelectNode : BaseFlowNode
    {
        private SelectNodeDefinition? _nodeConfig;

        public SelectNode()
        {
            NodeType = "Select";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Multiple output handles on the right
            var outputCount = _nodeConfig?.OutputCount ?? 2;
            for (int i = 0; i < outputCount; i++)
            {
                _outputHandles.Add(new NodeHandle
                {
                    Id = $"output-{i}",
                    Name = $"Option {i + 1}",
                    Type = NodeHandleType.Output,
                    Position = "right"
                });
            }

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new SelectNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata,
                    OutputCount = definition.GetConfigValue<int>("outputCount", 2)
                };

                // Reinitialize handles with the correct output count
                InitializeHandles();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate output count
            if (_nodeConfig.OutputCount < 2)
            {
                result.Errors.Add("Select node must have at least 2 output options");
            }

            if (_nodeConfig.OutputCount > 10)
            {
                result.Warnings.Add("Select node has many options, consider if this is optimal for selection logic");
            }

            // Validate that there are connected nodes for selection
            var connectedOutputs = context.AllEdges
                .Where(e => e.Source == NodeId)
                .Select(e => e.SourceHandle)
                .Distinct()
                .Count();

            if (connectedOutputs == 0)
            {
                result.Warnings.Add("Select node has no connected outputs");
            }

            if (connectedOutputs < 2)
            {
                result.Warnings.Add("Select node should have multiple connected outputs for meaningful selection");
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // For select nodes, the execution logic involves trying each option until one succeeds
                // The flow engine will handle the selection logic by executing options in order
                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "select",
                    ["outputCount"] = _nodeConfig.OutputCount,
                    ["selectionOptions"] = Enumerable.Range(0, _nodeConfig.OutputCount).ToList(),
                    ["selectionStrategy"] = "firstSuccess" // Could be configurable in the future
                };

                // Store selection information in execution context
                context.ExecutionData["selectNodeId"] = NodeId;
                context.ExecutionData["selectOutputCount"] = _nodeConfig.OutputCount;
                context.ExecutionData["selectCurrentIndex"] = 0;
                context.ExecutionData["selectExecutionId"] = Guid.NewGuid().ToString();

                // The flow engine will use this information to execute options until one succeeds
                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata about selection execution
                result.Metadata["executionMode"] = "select";
                result.Metadata["requiresSelectionExecution"] = true;
                result.Metadata["selectionStrategy"] = "firstSuccess";

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Select node: {ex.Message}", ex);
            }
        }

        public override async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            if (executionResult.Status == NodeExecutionStatus.Completed && _nodeConfig != null)
            {
                // For select nodes, we return all options but the flow engine will execute them
                // in order until one succeeds
                var selectExecutionId = context.ExecutionData.GetValueOrDefault("selectExecutionId", Guid.NewGuid().ToString());

                for (int i = 0; i < _nodeConfig.OutputCount; i++)
                {
                    var outputHandle = $"output-{i}";
                    
                    // The actual next node IDs would be determined by the flow engine
                    // based on the edges connected to this select node
                    if (executionResult.NextNodeIds.Count > i)
                    {
                        nextNodes.Add(new NextNodeInfo
                        {
                            NodeId = executionResult.NextNodeIds[i],
                            OutputHandle = outputHandle,
                            InputHandle = "input",
                            TransferData = new Dictionary<string, object>
                            {
                                ["selectionIndex"] = i,
                                ["selectionTotal"] = _nodeConfig.OutputCount,
                                ["selectExecutionId"] = selectExecutionId,
                                ["isSelectionExecution"] = true,
                                ["selectionPriority"] = i // Lower index = higher priority
                            }
                        });
                    }
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// Gets the configured output count.
        /// </summary>
        public int GetOutputCount()
        {
            return _nodeConfig?.OutputCount ?? 2;
        }
    }
}
