using Amazon.DynamoDBv2.DataModel;
using shared.Models.Enums;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(SignupProcess))]
    public class SignupProcess : DynamoDBModel
    {

        [DynamoDBHashKey]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string Email { get; set; } = string.Empty;

        public string AccountId { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<SignupStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<SignupStatus>))]
        public SignupStatus Status { get; set; }

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(SignupProcess.Id);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table has no range key
                return null;
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(SignupProcess);
        }
    }
}
