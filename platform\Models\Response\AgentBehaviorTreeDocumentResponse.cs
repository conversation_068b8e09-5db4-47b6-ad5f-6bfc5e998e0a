using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Models.Response;

namespace platform.Models.Response
{
    public class AgentBehaviorTreeDocumentResponse : DynamoDBModelResponse
    {
        public string Id { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonAgentBehaviorTree TreeDefinition { get; set; } = new JsonAgentBehaviorTree();
    }
}
