﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Models.Document;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentBehaviorTreeDocument))]
    public class AgentBehaviorTreeDocument : DynamoDBModel
    {
        public const string AccountIdIdIndex = "AccountId-Id-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdIdIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string AgentId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdIdIndex)]
        public string Id { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonAgentBehaviorTree TreeDefinition { get; set; } = new JsonAgentBehaviorTree();

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(AgentBehaviorTreeDocument.AccountId);
            }
            else if (indexName == AccountIdIdIndex)
            {
                // AccountId-Id-index hash key
                return nameof(AgentBehaviorTreeDocument.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(AgentBehaviorTreeDocument.AgentId);
            }
            else if (indexName == AccountIdIdIndex)
            {
                // AccountId-Id-index range key
                return nameof(AgentBehaviorTreeDocument.Id);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentBehaviorTreeDocument);
        }
    }
}
