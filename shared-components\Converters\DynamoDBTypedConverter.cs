using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using shared.Converters;
using System.Text;
using System.Text.Json;

namespace shared.Converters
{
    public class DynamoDBTypedConverter<T> : IPropertyConverter where T : notnull
    {
 
        public object? FromEntry(DynamoDBEntry entry)
        {
            ReadOnlySpan<byte> mem = new ReadOnlySpan<byte>(Encoding.UTF8.GetBytes(entry.AsDocument().ToJson()));
            JsonTypedConverter<T> converter = new JsonTypedConverter<T>();
            JsonSerializerOptions options = new JsonSerializerOptions();
            Utf8JsonReader jsonReader = new Utf8JsonReader(mem);
            return converter.Read(ref jsonReader, typeof(object), options);
        }

        public DynamoDBEntry ToEntry(object value)
        {
            MemoryStream stream = new MemoryStream();
            JsonTypedConverter<T> converter = new JsonTypedConverter<T>();
            JsonSerializerOptions options = new JsonSerializerOptions();
            Utf8JsonWriter utf8JsonWriter = new Utf8JsonWriter(stream);
            converter.Write(utf8JsonWriter, (T)value, options);
            stream.WriteByte(125);
            return Document.FromJson(Encoding.UTF8.GetString(stream.ToArray()));
        }
    }

    
}
