﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    public class JsonEnumStringConverter<TEnum> : JsonConverter<TEnum>
    {
        public override TEnum? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return (TEnum)Enum.Parse(typeof(TEnum), reader.GetString() ?? default(TEnum)?.ToString() ?? string.Empty);
        }

        public override void Write(Utf8JsonWriter writer, TEnum value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }
}
