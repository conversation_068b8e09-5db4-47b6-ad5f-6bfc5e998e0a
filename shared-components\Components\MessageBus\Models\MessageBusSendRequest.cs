using shared.Models.Enums;
using System.Security.Claims;

namespace shared.Components.MessageBus.Models
{
    /// <summary>
    /// Request object for sending messages through the message bus
    /// </summary>
    /// <typeparam name="T">Type of the payload</typeparam>
    public class MessageBusSendRequest<T>
    {
        /// <summary>
        /// The message payload
        /// </summary>
        public T Payload { get; set; } = default(T)!;

        /// <summary>
        /// Target microservice
        /// </summary>
        public MicroserviceType Target { get; set; }

        /// <summary>
        /// Controller name for routing
        /// </summary>
        public string Controller { get; set; } = string.Empty;

        /// <summary>
        /// Route name for routing
        /// </summary>
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// Security claims for authentication
        /// </summary>
        public List<Claim>? Claims { get; set; }

        /// <summary>
        /// Delay in seconds before processing
        /// </summary>
        public int DelayInSeconds { get; set; } = 0;

        /// <summary>
        /// Maximum retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// HTTP method for the request
        /// </summary>
        public HttpMethod Method { get; set; } = HttpMethod.Post;

        /// <summary>
        /// Additional metadata for the message
        /// </summary>
        public Dictionary<string, object>? Metadata { get; set; }

        /// <summary>
        /// Default constructor
        /// </summary>
        public MessageBusSendRequest()
        {
        }

        /// <summary>
        /// Constructor with required parameters
        /// </summary>
        /// <param name="payload">The message payload</param>
        /// <param name="target">Target microservice</param>
        /// <param name="controller">Controller name</param>
        /// <param name="route">Route name</param>
        public MessageBusSendRequest(T payload, MicroserviceType target, string controller, string route)
        {
            Payload = payload;
            Target = target;
            Controller = controller;
            Route = route;
        }

        /// <summary>
        /// Get the endpoint path for routing
        /// </summary>
        /// <returns>Combined controller/route path</returns>
        public string GetEndpoint()
        {
            return $"{Controller}/{Route}";
        }

        /// <summary>
        /// Validate the request
        /// </summary>
        /// <returns>True if valid</returns>
        public bool IsValid()
        {
            return Payload != null &&
                   !string.IsNullOrWhiteSpace(Controller) &&
                   !string.IsNullOrWhiteSpace(Route) &&
                   Target != MicroserviceType.Unknown &&
                   DelayInSeconds >= 0 &&
                   MaxRetries >= 0;
        }
    }
}
