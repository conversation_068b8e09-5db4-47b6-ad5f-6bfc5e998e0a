using System.ComponentModel;

namespace shared.Models.Enums
{
    public enum KnowledgeBaseStatus
    {
        [Description("QUEUED")]
        QUEUED = 0,

        [Description("DB_ENTRY_CREATED")]
        DB_ENTRY_CREATED = 10,

        [Description("KB_CREATED")]
        KB_CREATED = 20,

        [Description("DATASOURCE_CREATED")]
        DATASOURCE_CREATED = 30,

        [Description("READY")]
        READY = 50,

        [Description("SEALING")]
        SEALING = 60,

        [Description("DEPLOYING")]
        DEPLOYING = 70,

        [Description("SYNCRONIZING")]
        SYNCRONIZING = 80,

        [Description("TAGGING")]
        TAGGING = 90,

        [Description("DISABLED")]
        DISABLED = 100,

        [Description("FAILED")]
        FAILED = 999

    }
}
