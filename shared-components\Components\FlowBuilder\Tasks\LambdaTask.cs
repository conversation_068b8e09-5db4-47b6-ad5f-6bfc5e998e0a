using Amazon.Lambda;
using Amazon.Lambda.Model;
using shared.Components.FlowBuilder.Models;
using System.Text.Json;

namespace shared.Components.FlowBuilder.Tasks
{
    /// <summary>
    /// AWS Lambda Task - Executes AWS Lambda functions with configurable parameters.
    /// This is an example of a cloud provider specific implementation.
    /// </summary>
    public class LambdaTask : BaseFlowTask
    {
        private LambdaTaskDefinition? _taskConfig;
        private readonly IAmazonLambda? _lambdaClient;

        public LambdaTask(IAmazonLambda? lambdaClient = null)
        {
            TaskType = "LambdaTask";
            IsLongRunning = true; // Lambda functions can be long-running
            SupportsStreaming = true;
            _lambdaClient = lambdaClient;
        }

        protected override async Task<bool> InitializeTaskSpecificAsync(FlowTaskDefinition definition)
        {
            try
            {
                _taskConfig = new LambdaTaskDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Configuration = definition.Configuration,
                    Metadata = definition.Metadata,
                    FunctionName = definition.GetConfigValue<string>("functionName", ""),
                    Region = definition.GetConfigValue<string>("region", "us-east-1"),
                    IsAsync = definition.GetConfigValue<bool>("isAsync", false),
                    TimeoutSeconds = definition.GetConfigValue<int>("timeoutSeconds", 30)
                };

                // Extract payload from configuration
                if (definition.Configuration.ContainsKey("payload"))
                {
                    _taskConfig.Payload = (Dictionary<string, object>)(definition.Configuration["payload"] ?? new Dictionary<string, object>());
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateTaskSpecificAsync(FlowValidationContext context, TaskValidationResult result)
        {
            await Task.CompletedTask;

            if (_taskConfig == null)
            {
                result.Errors.Add("Task configuration is not initialized");
                return;
            }

            // Validate function name
            if (string.IsNullOrEmpty(_taskConfig.FunctionName))
            {
                result.Errors.Add("Lambda function name is required");
            }

            // Validate region
            if (string.IsNullOrEmpty(_taskConfig.Region))
            {
                result.Errors.Add("AWS region is required");
            }

            // Validate timeout
            if (_taskConfig.TimeoutSeconds <= 0 || _taskConfig.TimeoutSeconds > 900)
            {
                result.Errors.Add("Timeout must be between 1 and 900 seconds");
            }

            // Check if Lambda client is available
            if (_lambdaClient == null)
            {
                result.Warnings.Add("Lambda client is not configured - task will fail at runtime");
            }
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_taskConfig == null)
                {
                    return CreateFailureResult("Task configuration is not initialized");
                }

                if (_lambdaClient == null)
                {
                    return CreateFailureResult("Lambda client is not configured");
                }

                FireStreamResult("message", $"Starting Lambda task: {Name}");

                // Prepare payload with variable substitution
                var payload = PreparePayload(_taskConfig.Payload, context);
                var payloadJson = JsonSerializer.Serialize(payload);

                FireStreamResult("progress", new { step = "invoking_lambda", functionName = _taskConfig.FunctionName });

                // Create invoke request
                var request = new InvokeRequest
                {
                    FunctionName = _taskConfig.FunctionName,
                    Payload = payloadJson,
                    InvocationType = _taskConfig.IsAsync ? InvocationType.Event : InvocationType.RequestResponse
                };

                // Invoke Lambda function
                var response = await _lambdaClient.InvokeAsync(request, cancellationToken);

                // Process response
                var outputData = new Dictionary<string, object>
                {
                    ["functionName"] = _taskConfig.FunctionName,
                    ["statusCode"] = response.StatusCode,
                    ["executedVersion"] = response.ExecutedVersion ?? "LATEST",
                    ["logResult"] = response.LogResult ?? "",
                    ["isAsync"] = _taskConfig.IsAsync
                };

                if (!_taskConfig.IsAsync && response.Payload != null)
                {
                    // For synchronous invocations, parse the response payload
                    using var reader = new StreamReader(response.Payload);
                    var responsePayload = await reader.ReadToEndAsync();
                    
                    try
                    {
                        var responseData = JsonSerializer.Deserialize<Dictionary<string, object>>(responsePayload);
                        outputData["response"] = responseData ?? new Dictionary<string, object>();
                    }
                    catch
                    {
                        outputData["response"] = responsePayload;
                    }
                }

                // Check for function errors
                if (!string.IsNullOrEmpty(response.FunctionError))
                {
                    outputData["functionError"] = response.FunctionError;
                    FireStreamResult("error", new { functionError = response.FunctionError });
                    
                    return new TaskExecutionResult
                    {
                        TaskId = TaskId,
                        Status = TaskExecutionStatus.Failed,
                        ErrorMessage = $"Lambda function error: {response.FunctionError}",
                        OutputData = outputData,
                        ExecutionTime = DateTime.UtcNow - startTime,
                        CompletedAt = DateTime.UtcNow
                    };
                }

                // Set variable fields
                context.SetFieldValue($"lambda.{TaskId}.statusCode", response.StatusCode);
                context.SetFieldValue($"lambda.{TaskId}.executedVersion", response.ExecutedVersion ?? "LATEST");

                FireStreamResult("completed", new { 
                    message = "Lambda task completed successfully", 
                    statusCode = response.StatusCode,
                    functionName = _taskConfig.FunctionName
                });

                var result = CreateSuccessResult(outputData, true); // Lambda tasks can affect flow execution
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata
                result.Metadata["functionName"] = _taskConfig.FunctionName;
                result.Metadata["statusCode"] = response.StatusCode;
                result.Metadata["isAsync"] = _taskConfig.IsAsync;

                return result;
            }
            catch (OperationCanceledException)
            {
                FireStreamResult("cancelled", "Lambda task was cancelled");
                return new TaskExecutionResult
                {
                    TaskId = TaskId,
                    Status = TaskExecutionStatus.Cancelled,
                    CompletedAt = DateTime.UtcNow,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
            catch (Exception ex)
            {
                FireStreamResult("error", new { error = ex.Message });
                return CreateFailureResult($"Error executing Lambda task: {ex.Message}", ex);
            }
        }

        protected override async Task StartLongRunningTaskSpecificAsync(string sessionId, FlowExecutionContext context)
        {
            // For async Lambda invocations, we start the execution and track it
            try
            {
                if (_taskConfig == null || _lambdaClient == null)
                {
                    _executionSessions[sessionId] = "failed";
                    return;
                }

                _executionSessions[sessionId] = "running";

                // Prepare payload
                var payload = PreparePayload(_taskConfig.Payload, context);
                var payloadJson = JsonSerializer.Serialize(payload);

                // Invoke Lambda asynchronously
                var request = new InvokeRequest
                {
                    FunctionName = _taskConfig.FunctionName,
                    Payload = payloadJson,
                    InvocationType = InvocationType.Event
                };

                var response = await _lambdaClient.InvokeAsync(request);

                if (response.StatusCode == 202) // Accepted
                {
                    _executionSessions[sessionId] = "completed";
                }
                else
                {
                    _executionSessions[sessionId] = "failed";
                }
            }
            catch (Exception)
            {
                _executionSessions[sessionId] = "failed";
            }
        }

        /// <summary>
        /// Prepare payload with variable substitution.
        /// </summary>
        private Dictionary<string, object> PreparePayload(Dictionary<string, object> originalPayload, FlowExecutionContext context)
        {
            var payload = new Dictionary<string, object>();

            foreach (var kvp in originalPayload)
            {
                var value = kvp.Value;

                // If value is a string, perform variable substitution
                if (value is string stringValue)
                {
                    value = SubstituteVariables(stringValue, context);
                }

                payload[kvp.Key] = value;
            }

            // Add context information
            payload["_flowContext"] = new
            {
                sessionId = context.SessionId,
                flowId = context.FlowId,
                accountId = context.AccountId,
                taskId = TaskId
            };

            return payload;
        }

        /// <summary>
        /// Substitute variables in a string value.
        /// </summary>
        private string SubstituteVariables(string value, FlowExecutionContext context)
        {
            var result = value;

            // Replace field values
            foreach (var field in context.InputFields)
            {
                var placeholder = $"{{{field.Key}}}";
                if (result.Contains(placeholder))
                {
                    result = result.Replace(placeholder, field.Value?.ToString() ?? "");
                }
            }

            foreach (var field in context.VariableFields)
            {
                var placeholder = $"{{{field.Key}}}";
                if (result.Contains(placeholder))
                {
                    result = result.Replace(placeholder, field.Value?.ToString() ?? "");
                }
            }

            // Replace common placeholders
            result = result.Replace("{timestamp}", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));
            result = result.Replace("{sessionId}", context.SessionId);
            result = result.Replace("{flowId}", context.FlowId);
            result = result.Replace("{taskId}", TaskId);

            return result;
        }

        /// <summary>
        /// Gets the configured function name.
        /// </summary>
        public string GetFunctionName()
        {
            return _taskConfig?.FunctionName ?? "";
        }

        /// <summary>
        /// Gets whether the task is configured for async execution.
        /// </summary>
        public bool IsAsyncExecution()
        {
            return _taskConfig?.IsAsync ?? false;
        }
    }
}
