# MessageBus Component

A comprehensive message bus implementation that replaces the ApiEventBus with improved functionality, reliability, and performance.

## Features

- **Interface-based Design**: Clean separation of concerns with `IMessageBus` interface
- **External Queue Support**: AWS SQS integration with automatic queue management
- **Local Route Optimization**: Direct method invocation for same-microservice calls
- **Non-blocking Processing**: Asynchronous message processing to maximize consumer throughput
- **Message Deduplication**: Optional IMemoryCache-based duplicate detection with automatic expiration
- **Enhanced Request Model**: `MessageBusSendRequest<T>` for structured message sending
- **Delay Support**: Queue-native message scheduling
- **Retry Logic**: Configurable retry policies with exponential backoff
- **Queue Management**: Automatic queue creation and verification

## Core Components

### IMessageBus Interface

```csharp
public interface IMessageBus : IHostedService
{
    Task<string?> SendAsync<T>(T payload, MicroserviceType target, string controller, string route, 
        List<Claim>? claims = null, int delayInSeconds = 0, int maxRetries = 3);
    
    Task<string?> SendAsync<T>(EventBusTriggeredMessage<T> message);
    

}
```

### EventBusTriggeredMessage<T>

The enhanced message wrapper that includes:
- **Payload**: The actual message data of type T
- **Routing information**: Target microservice, controller, and route
- **Retry metadata**: Current retry count, max retries, error tracking
- **Timing information**: Created timestamp, last processed timestamp

- **Security context**: Claims for authentication
- **Custom metadata**: Extensible dictionary for additional data

### SQS Implementation

The `SQSMessageBus` class provides:
- AWS SQS integration for external message delivery
- Automatic queue naming based on microservice configuration
- Message attributes for better filtering and routing
- Long polling for efficient message retrieval
- Automatic retry with exponential backoff
- Dead letter queue support through retry limits

## Usage Examples

### Basic Message Sending

```csharp
// Inject IMessageBus
private readonly IMessageBus _messageBus;

// Send a simple message
var payload = new { UserId = "123", Action = "UserCreated" };
var messageId = await _messageBus.SendAsync(
    payload, 
    MicroserviceType.AgentService, 
    "User", 
    "ProcessCreation"
);
```

### Advanced Message with Custom Settings

```csharp
var message = new EventBusTriggeredMessage<UserCreatedEvent>(
    new UserCreatedEvent { UserId = "123", Email = "<EMAIL>" },
    MicroserviceType.AgentService,
    "User",
    "ProcessCreation",
    claims: new List<Claim> { new Claim("AccountId", "456") },
    delayInSeconds: 30,
    maxRetries: 5
);

var messageId = await _messageBus.SendAsync(message);
```

### Scheduled Messages

```csharp
// Schedule for specific time
await _messageBus.ScheduleAsync(
    payload,
    MicroserviceType.Platform,
    "Notification",
    "SendReminder",
    DateTime.UtcNow.AddHours(24)
);

// Schedule with delay
await _messageBus.ScheduleAsync(
    payload,
    MicroserviceType.Platform,
    "Notification",
    "SendWelcome",
    TimeSpan.FromMinutes(5)
);
```



## Configuration

### Service Registration

```csharp
// In Program.cs or Startup.cs
services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
services.AddSingleton<IMessageBus, SQSMessageBus>();
```

### Microservice Configuration

```json
{
  "MicroserviceConfiguration": {
    "Environment": "development",
    "InstanceId": "dev01",
    "Type": "Platform",
    "Group": "default"
  }
}
```

## Local Method Invocation

The MessageBus attempts to invoke controller methods directly when the target microservice matches the current service, avoiding HTTP overhead:

1. **Method Discovery**: Uses reflection to find controller classes and methods
2. **Dependency Injection**: Creates controller instances using the service provider
3. **Parameter Mapping**: Automatically maps message payload to method parameters
4. **EventBusTriggeredMessage Support**: Methods can accept `EventBusTriggeredMessage<T>` parameters
5. **Fallback**: Falls back to HTTP calls if direct invocation fails

### Controller Method Example

```csharp
[ApiController]
[Route("[controller]")]
public class UserController : ControllerBase
{
    // Method that accepts EventBusTriggeredMessage
    [HttpPost("ProcessCreation")]
    public async Task<IActionResult> ProcessCreation(EventBusTriggeredMessage<UserCreatedEvent> message)
    {
        // Access the payload
        var userEvent = message.Payload;
        
        // Access metadata
        var retryCount = message.RetryCount;
        var source = message.Source;
        
        // Process the event
        await ProcessUserCreation(userEvent);
        
        return Ok();
    }
    
    // Method that accepts payload directly
    [HttpPost("ProcessUpdate")]
    public async Task<IActionResult> ProcessUpdate(UserUpdatedEvent userEvent)
    {
        await ProcessUserUpdate(userEvent);
        return Ok();
    }
}
```

## Retry Policies

### Built-in Policies

```csharp
// Default policy (3 retries, exponential backoff)
var policy = RetryPolicy.Default;

// Aggressive policy (5 retries, faster backoff)
var policy = RetryPolicy.Aggressive;

// Conservative policy (2 retries, slower backoff)
var policy = RetryPolicy.Conservative;

// No retries
var policy = RetryPolicy.None;
```

### Custom Retry Policy

```csharp
var customPolicy = new RetryPolicy
{
    MaxRetries = 4,
    BaseDelaySeconds = 2,
    MaxDelaySeconds = 120,
    BackoffMultiplier = 1.5,
    UseJitter = true,
    JitterFactor = 0.2
};
```

## Migration from ApiEventBus

### Key Differences

1. **Return Type**: `SendAsync` returns `Task<string?>` (message ID) instead of `Task<bool>`
2. **Message Wrapper**: Use `EventBusTriggeredMessage<T>` instead of raw payloads for enhanced metadata

4. **Improved Local Calls**: Direct method invocation instead of always using HTTP
5. **Enhanced Retry Logic**: Configurable retry policies with exponential backoff

### Migration Steps

1. Replace `IApiEventBus` with `IMessageBus` in dependency injection
2. Update method calls from `Send()` to `SendAsync()`
3. Handle the new return type (message ID instead of boolean)
4. Update controller methods to accept `EventBusTriggeredMessage<T>` if desired
5. Configure retry policies as needed
6. Test local method invocation functionality

### Example Migration

**Before (ApiEventBus):**
```csharp
var success = await _eventBus.Send(payload, target, controller, route, claims, delay);
```

**After (MessageBus):**
```csharp
var messageId = await _messageBus.SendAsync(payload, target, controller, route, claims, delay);
var success = !string.IsNullOrEmpty(messageId);
```

## Testing

The component includes comprehensive unit tests covering:
- Message sending and receiving
- Retry logic and policies
- Circuit breaker functionality
- Message scheduling

- Local method invocation

Run tests with:
```bash
dotnet test shared-components/Components/MessageBus/Tests/
```

## Performance Considerations

- **Local Method Invocation**: Significantly faster than HTTP calls for same-service communication
- **Connection Pooling**: SQS client uses connection pooling for external messages
- **Batch Processing**: SQS implementation processes up to 10 messages at once
- **Memory Usage**: Message status tracking uses in-memory dictionaries (consider external storage for high-volume scenarios)
- **Retry Delays**: Exponential backoff prevents overwhelming failed services

## Monitoring and Observability

The component provides extensive logging at various levels:
- **Information**: Successful message processing, queue operations
- **Warning**: Retry attempts, fallback to HTTP calls
- **Error**: Failed message processing, queue errors
- **Debug**: Detailed method invocation, parameter mapping

Use structured logging to track:
- Message throughput
- Retry rates
- Local vs. HTTP call ratios
- Processing latencies
- Error patterns
