using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentKnowledgeBase))]
    public class AgentKnowledgeBase : DynamoDBModel
    {
        public const string KnowledgebaseIdHashIndex = "KnowledgebaseId-AgentId-index";

        public string AccountId { get; set; } = string.Empty;

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(KnowledgebaseIdHashIndex)]
        public string AgentId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexHashKey(KnowledgebaseIdHashIndex)]
        public string KnowledgebaseId { get; set; } = string.Empty;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(AgentKnowledgeBase.AgentId);
            }
            else if (indexName == KnowledgebaseIdHashIndex)
            {
                // KnowledgebaseId-AgentId-index hash key
                return nameof(AgentKnowledgeBase.KnowledgebaseId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(AgentKnowledgeBase.KnowledgebaseId);
            }
            else if (indexName == KnowledgebaseIdHashIndex)
            {
                // KnowledgebaseId-AgentId-index range key
                return nameof(AgentKnowledgeBase.AgentId);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentKnowledgeBase);
        }
    }
}
