using shared.Models.Enums;

namespace shared.Components.MessageBus.Interfaces
{
    /// <summary>
    /// Interface for managing message bus queues
    /// </summary>
    public interface IMessageBusQueueManager
    {
        /// <summary>
        /// Check if a queue exists for the specified microservice
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>True if queue exists</returns>
        Task<bool> QueueExistsAsync(MicroserviceType microserviceType);

        /// <summary>
        /// Create a queue for the specified microservice
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>True if queue was created successfully</returns>
        Task<bool> CreateQueueAsync(MicroserviceType microserviceType);

        /// <summary>
        /// Ensure a queue exists for the specified microservice, creating it if necessary
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>True if queue exists or was created successfully</returns>
        Task<bool> EnsureQueueExistsAsync(MicroserviceType microserviceType);

        /// <summary>
        /// Get queue URL/name for the specified microservice
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>Queue URL or name</returns>
        Task<string> GetQueueUrlAsync(MicroserviceType microserviceType);

        /// <summary>
        /// Delete a queue for the specified microservice
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>True if queue was deleted successfully</returns>
        Task<bool> DeleteQueueAsync(MicroserviceType microserviceType);

        /// <summary>
        /// Get queue attributes such as message count, visibility timeout, etc.
        /// </summary>
        /// <param name="microserviceType">The microservice type</param>
        /// <returns>Dictionary of queue attributes</returns>
        Task<Dictionary<string, string>> GetQueueAttributesAsync(MicroserviceType microserviceType);
    }
}
