﻿using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;

namespace shared.Components.BehaviorTree.Node
{
    public class Selector : Node
    {
        public Selector(string nodeId, List<Node> children, ConditionExpression conditionExpression) : base(nodeId, children, conditionExpression) { }

        protected override async Task<NodeState> EvaluateImpl(TreeState treeState)
        {
            NodeState state;
            foreach (Node node in Children)
            {
                switch (await node.Evaluate(treeState))
                {
                    case NodeState.FAILURE:
                        continue;
                    case NodeState.SUCCESS:
                        state = NodeState.SUCCESS;
                        return state;
                    case NodeState.RUNNING:
                        state = NodeState.RUNNING;
                        return state;
                    default:
                        continue;
                }
            }

            state = NodeState.FAILURE;
            return state;
        }
    }
}
