using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using System.Collections.Concurrent;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using GenericEventBusInterface = shared.Components.GenericEventBus.Interfaces.IGenericEventBus;
using shared.Components.GenericEventBus.Models;

namespace shared.Components.FlowBuilder.ResultDelivery
{
    /// <summary>
    /// Service for delivering flow results and streaming data to various integration endpoints.
    /// Supports websockets, long HTTP requests, message queues, and webhooks.
    /// </summary>
    public class FlowResultDeliveryService : IFlowResultDelivery
    {
        private readonly ConcurrentDictionary<string, List<FlowDeliveryEndpoint>> _sessionEndpoints;
        private readonly HttpClient _httpClient;
        private readonly GenericEventBusInterface? _eventBus;

        public event EventHandler<DeliveryFailedEventArgs>? OnDeliveryFailed;
        public event EventHandler<DeliverySucceededEventArgs>? OnDeliverySucceeded;

        public FlowResultDeliveryService(HttpClient? httpClient = null, GenericEventBusInterface? eventBus = null)
        {
            _sessionEndpoints = new ConcurrentDictionary<string, List<FlowDeliveryEndpoint>>();
            _httpClient = httpClient ?? new HttpClient();
            _eventBus = eventBus;

            // Configure HTTP client
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task<bool> RegisterDeliveryEndpointAsync(string sessionId, FlowDeliveryEndpoint endpoint)
        {
            await Task.CompletedTask;

            try
            {
                _sessionEndpoints.AddOrUpdate(sessionId,
                    new List<FlowDeliveryEndpoint> { endpoint },
                    (key, existing) =>
                    {
                        existing.Add(endpoint);
                        return existing;
                    });

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UnregisterDeliveryEndpointAsync(string sessionId, string endpointId)
        {
            await Task.CompletedTask;

            if (_sessionEndpoints.TryGetValue(sessionId, out var endpoints))
            {
                var removed = endpoints.RemoveAll(e => e.Id == endpointId);
                return removed > 0;
            }

            return false;
        }

        public async Task<IList<DeliveryResult>> DeliverFlowResultAsync(string sessionId, FlowExecutionResult result)
        {
            var deliveryResults = new List<DeliveryResult>();

            if (!_sessionEndpoints.TryGetValue(sessionId, out var endpoints))
            {
                return deliveryResults;
            }

            var activeEndpoints = endpoints.Where(e => e.IsActive && e.DeliverResults).ToList();

            foreach (var endpoint in activeEndpoints)
            {
                var deliveryResult = await DeliverToEndpointAsync(endpoint, "flow_result", result);
                deliveryResults.Add(deliveryResult);

                if (deliveryResult.Success)
                {
                    OnDeliverySucceeded?.Invoke(this, new DeliverySucceededEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        DeliveryTime = deliveryResult.DeliveryTime
                    });
                }
                else
                {
                    OnDeliveryFailed?.Invoke(this, new DeliveryFailedEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        Exception = new Exception(deliveryResult.ErrorMessage ?? "Unknown delivery error"),
                        RetryCount = 0,
                        WillRetry = false
                    });
                }
            }

            return deliveryResults;
        }

        public async Task<IList<DeliveryResult>> DeliverStreamDataAsync(string sessionId, FlowStreamData streamData)
        {
            var deliveryResults = new List<DeliveryResult>();

            if (!_sessionEndpoints.TryGetValue(sessionId, out var endpoints))
            {
                return deliveryResults;
            }

            var activeEndpoints = endpoints.Where(e => e.IsActive && e.DeliverStreaming).ToList();

            foreach (var endpoint in activeEndpoints)
            {
                var deliveryResult = await DeliverToEndpointAsync(endpoint, "stream_data", streamData);
                deliveryResults.Add(deliveryResult);

                if (deliveryResult.Success)
                {
                    OnDeliverySucceeded?.Invoke(this, new DeliverySucceededEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        DeliveryTime = deliveryResult.DeliveryTime
                    });
                }
                else
                {
                    OnDeliveryFailed?.Invoke(this, new DeliveryFailedEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        Exception = new Exception(deliveryResult.ErrorMessage ?? "Unknown delivery error"),
                        RetryCount = 0,
                        WillRetry = false
                    });
                }
            }

            return deliveryResults;
        }

        public async Task<IList<DeliveryResult>> DeliverStatusUpdateAsync(string sessionId, FlowStatusUpdate statusUpdate)
        {
            var deliveryResults = new List<DeliveryResult>();

            if (!_sessionEndpoints.TryGetValue(sessionId, out var endpoints))
            {
                return deliveryResults;
            }

            var activeEndpoints = endpoints.Where(e => e.IsActive && e.DeliverStatusUpdates).ToList();

            foreach (var endpoint in activeEndpoints)
            {
                var deliveryResult = await DeliverToEndpointAsync(endpoint, "status_update", statusUpdate);
                deliveryResults.Add(deliveryResult);

                if (deliveryResult.Success)
                {
                    OnDeliverySucceeded?.Invoke(this, new DeliverySucceededEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        DeliveryTime = deliveryResult.DeliveryTime
                    });
                }
                else
                {
                    OnDeliveryFailed?.Invoke(this, new DeliveryFailedEventArgs
                    {
                        SessionId = sessionId,
                        EndpointId = endpoint.Id,
                        Exception = new Exception(deliveryResult.ErrorMessage ?? "Unknown delivery error"),
                        RetryCount = 0,
                        WillRetry = false
                    });
                }
            }

            return deliveryResults;
        }

        public async Task<IList<FlowDeliveryEndpoint>> GetDeliveryEndpointsAsync(string sessionId)
        {
            await Task.CompletedTask;

            if (_sessionEndpoints.TryGetValue(sessionId, out var endpoints))
            {
                return endpoints.ToList();
            }

            return new List<FlowDeliveryEndpoint>();
        }

        public async Task<EndpointTestResult> TestEndpointAsync(FlowDeliveryEndpoint endpoint)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                switch (endpoint.Type)
                {
                    case FlowDeliveryType.Webhook:
                    case FlowDeliveryType.LongHttp:
                        return await TestHttpEndpointAsync(endpoint, startTime);

                    case FlowDeliveryType.WebSocket:
                        return await TestWebSocketEndpointAsync(endpoint, startTime);

                    case FlowDeliveryType.MessageQueue:
                        return await TestMessageQueueEndpointAsync(endpoint, startTime);

                    case FlowDeliveryType.EventBus:
                        return await TestEventBusEndpointAsync(endpoint, startTime);

                    case FlowDeliveryType.Custom:
                        return await TestCustomEndpointAsync(endpoint, startTime);

                    default:
                        return new EndpointTestResult
                        {
                            IsReachable = false,
                            ErrorMessage = $"Unsupported endpoint type: {endpoint.Type}",
                            ResponseTime = DateTime.UtcNow - startTime
                        };
                }
            }
            catch (Exception ex)
            {
                return new EndpointTestResult
                {
                    IsReachable = false,
                    ErrorMessage = ex.Message,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Deliver data to a specific endpoint.
        /// </summary>
        private async Task<DeliveryResult> DeliverToEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                switch (endpoint.Type)
                {
                    case FlowDeliveryType.Webhook:
                    case FlowDeliveryType.LongHttp:
                        return await DeliverToHttpEndpointAsync(endpoint, dataType, data, startTime);

                    case FlowDeliveryType.WebSocket:
                        return await DeliverToWebSocketEndpointAsync(endpoint, dataType, data, startTime);

                    case FlowDeliveryType.MessageQueue:
                        return await DeliverToMessageQueueEndpointAsync(endpoint, dataType, data, startTime);

                    case FlowDeliveryType.EventBus:
                        return await DeliverToEventBusEndpointAsync(endpoint, dataType, data, startTime);

                    case FlowDeliveryType.Custom:
                        return await DeliverToCustomEndpointAsync(endpoint, dataType, data, startTime);

                    default:
                        return new DeliveryResult
                        {
                            EndpointId = endpoint.Id,
                            Success = false,
                            ErrorMessage = $"Unsupported endpoint type: {endpoint.Type}",
                            DeliveryTime = DateTime.UtcNow - startTime
                        };
                }
            }
            catch (Exception ex)
            {
                return new DeliveryResult
                {
                    EndpointId = endpoint.Id,
                    Success = false,
                    ErrorMessage = ex.Message,
                    DeliveryTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Deliver to HTTP endpoint (webhook or long HTTP).
        /// </summary>
        private async Task<DeliveryResult> DeliverToHttpEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data, DateTime startTime)
        {
            var payload = new
            {
                type = dataType,
                data = data,
                timestamp = DateTime.UtcNow,
                endpointId = endpoint.Id
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Add custom headers
            foreach (var header in endpoint.Headers)
            {
                content.Headers.Add(header.Key, header.Value);
            }

            var response = await _httpClient.PostAsync(endpoint.Url, content);

            return new DeliveryResult
            {
                EndpointId = endpoint.Id,
                Success = response.IsSuccessStatusCode,
                HttpStatusCode = (int)response.StatusCode,
                ErrorMessage = response.IsSuccessStatusCode ? null : $"HTTP {response.StatusCode}: {response.ReasonPhrase}",
                DeliveryTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Deliver to WebSocket endpoint.
        /// </summary>
        private async Task<DeliveryResult> DeliverToWebSocketEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data, DateTime startTime)
        {
            // WebSocket delivery would require maintaining persistent connections
            // This is a placeholder implementation
            await Task.CompletedTask;

            return new DeliveryResult
            {
                EndpointId = endpoint.Id,
                Success = false,
                ErrorMessage = "WebSocket delivery not implemented",
                DeliveryTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Deliver to message queue endpoint.
        /// </summary>
        private async Task<DeliveryResult> DeliverToMessageQueueEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data, DateTime startTime)
        {
            // Message queue delivery would require queue-specific implementations
            // This is a placeholder implementation
            await Task.CompletedTask;

            return new DeliveryResult
            {
                EndpointId = endpoint.Id,
                Success = false,
                ErrorMessage = "Message queue delivery not implemented",
                DeliveryTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Deliver to event bus endpoint.
        /// </summary>
        private async Task<DeliveryResult> DeliverToEventBusEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data, DateTime startTime)
        {
            if (_eventBus == null)
            {
                return new DeliveryResult
                {
                    EndpointId = endpoint.Id,
                    Success = false,
                    ErrorMessage = "Event bus not configured",
                    DeliveryTime = DateTime.UtcNow - startTime
                };
            }

            var message = new GenericEventMessage
            {
                MessageType = dataType,
                Source = "FlowResultDelivery",
                Target = endpoint.Configuration.GetValueOrDefault("target", "")?.ToString() ?? "",
                Payload = data
            };

            var messageId = await _eventBus.SendMessageAsync(message);

            return new DeliveryResult
            {
                EndpointId = endpoint.Id,
                Success = !string.IsNullOrEmpty(messageId),
                ErrorMessage = string.IsNullOrEmpty(messageId) ? "Failed to send message to event bus" : null,
                DeliveryTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Deliver to custom endpoint.
        /// </summary>
        private async Task<DeliveryResult> DeliverToCustomEndpointAsync(FlowDeliveryEndpoint endpoint, string dataType, object data, DateTime startTime)
        {
            // Custom delivery would be implemented based on specific requirements
            // This is a placeholder implementation
            await Task.CompletedTask;

            return new DeliveryResult
            {
                EndpointId = endpoint.Id,
                Success = false,
                ErrorMessage = "Custom delivery not implemented",
                DeliveryTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Test HTTP endpoint connectivity.
        /// </summary>
        private async Task<EndpointTestResult> TestHttpEndpointAsync(FlowDeliveryEndpoint endpoint, DateTime startTime)
        {
            try
            {
                var response = await _httpClient.GetAsync(endpoint.Url);
                
                return new EndpointTestResult
                {
                    IsReachable = true,
                    HttpStatusCode = (int)response.StatusCode,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
            catch (Exception ex)
            {
                return new EndpointTestResult
                {
                    IsReachable = false,
                    ErrorMessage = ex.Message,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Test WebSocket endpoint connectivity.
        /// </summary>
        private async Task<EndpointTestResult> TestWebSocketEndpointAsync(FlowDeliveryEndpoint endpoint, DateTime startTime)
        {
            await Task.CompletedTask;
            
            return new EndpointTestResult
            {
                IsReachable = false,
                ErrorMessage = "WebSocket testing not implemented",
                ResponseTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Test message queue endpoint connectivity.
        /// </summary>
        private async Task<EndpointTestResult> TestMessageQueueEndpointAsync(FlowDeliveryEndpoint endpoint, DateTime startTime)
        {
            await Task.CompletedTask;
            
            return new EndpointTestResult
            {
                IsReachable = false,
                ErrorMessage = "Message queue testing not implemented",
                ResponseTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Test event bus endpoint connectivity.
        /// </summary>
        private async Task<EndpointTestResult> TestEventBusEndpointAsync(FlowDeliveryEndpoint endpoint, DateTime startTime)
        {
            await Task.CompletedTask;
            
            return new EndpointTestResult
            {
                IsReachable = _eventBus != null,
                ErrorMessage = _eventBus == null ? "Event bus not configured" : null,
                ResponseTime = DateTime.UtcNow - startTime
            };
        }

        /// <summary>
        /// Test custom endpoint connectivity.
        /// </summary>
        private async Task<EndpointTestResult> TestCustomEndpointAsync(FlowDeliveryEndpoint endpoint, DateTime startTime)
        {
            await Task.CompletedTask;
            
            return new EndpointTestResult
            {
                IsReachable = false,
                ErrorMessage = "Custom endpoint testing not implemented",
                ResponseTime = DateTime.UtcNow - startTime
            };
        }
    }
}
