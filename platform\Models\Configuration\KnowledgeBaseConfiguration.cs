using shared.Models.Configuration;

namespace platform.Models.Configuration
{
    public class BedrockConfiguration
    {
        public string KnowledgeBaseBucketArn { get; set; } = string.Empty;
        public string KnowledgeBaseRoleName { get; set; } = string.Empty;
        public string AgentRoleName { get; set; } = string.Empty;
        public string VectorStoreSecretArn { get; set; } = string.Empty;
        public string VectorStorePath { get; set; } = string.Empty;
        public string DefaultEmbeddingsModelArn { get; set; } = string.Empty;
        public PineconeConfiguration PineconeConfiguration { get; set; } = new PineconeConfiguration();
    }
}
