using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Base abstract class for all flow nodes providing common functionality.
    /// </summary>
    public abstract class BaseFlowNode : IFlowNode
    {
        public string NodeId { get; protected set; } = string.Empty;
        public string NodeType { get; protected set; } = string.Empty;
        public string Name { get; protected set; } = string.Empty;
        public FlowNodeDefinition Definition { get; protected set; } = new();
        public IReadOnlyList<NodeHandle> InputHandles { get; protected set; } = new List<NodeHandle>();
        public IReadOnlyList<NodeHandle> OutputHandles { get; protected set; } = new List<NodeHandle>();

        protected List<NodeHandle> _inputHandles = new();
        protected List<NodeHandle> _outputHandles = new();

        public virtual async Task<bool> InitializeAsync(FlowNodeDefinition definition)
        {
            Definition = definition;
            NodeId = definition.Id;
            NodeType = definition.Type;
            Name = definition.Name;

            // Initialize handles
            InitializeHandles();

            // Perform node-specific initialization
            return await InitializeNodeSpecificAsync(definition);
        }

        public virtual async Task<NodeValidationResult> ValidateAsync(FlowValidationContext context)
        {
            var result = new NodeValidationResult { IsValid = true };

            // Basic validation
            if (string.IsNullOrEmpty(NodeId))
                result.Errors.Add("Node ID cannot be empty");

            if (string.IsNullOrEmpty(Name))
                result.Errors.Add("Node name cannot be empty");

            // Validate handles
            ValidateHandles(result);

            // Perform node-specific validation
            await ValidateNodeSpecificAsync(context, result);

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        public virtual async Task<bool> EvaluateConditionsAsync(FlowExecutionContext context)
        {
            if (Definition.Conditions == null)
                return true;

            return await EvaluateRuleGroupAsync(Definition.Conditions, context);
        }

        public abstract Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default);

        public virtual async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            // Default implementation - override in specific node types for custom logic
            if (executionResult.Status == NodeExecutionStatus.Completed)
            {
                // Get the first output handle and find connected nodes
                var outputHandle = OutputHandles.FirstOrDefault();
                if (outputHandle != null)
                {
                    // This would typically be resolved by the flow engine using edge information
                    // For now, we'll return the next node IDs from the execution result
                    foreach (var nodeId in executionResult.NextNodeIds)
                    {
                        nextNodes.Add(new NextNodeInfo
                        {
                            NodeId = nodeId,
                            OutputHandle = outputHandle.Id,
                            InputHandle = "input" // Default input handle
                        });
                    }
                }
            }

            return nextNodes;
        }

        public virtual async Task OnExecutionCompletedAsync(NodeExecutionResult result, FlowExecutionContext context)
        {
            // Default implementation - can be overridden for cleanup or logging
            await Task.CompletedTask;
        }

        public virtual async Task OnExecutionCancelledAsync(FlowExecutionContext context)
        {
            // Default implementation - can be overridden for cleanup
            await Task.CompletedTask;
        }

        /// <summary>
        /// Initialize node-specific handles. Override in derived classes.
        /// </summary>
        protected virtual void InitializeHandles()
        {
            // Default: single input and output
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            _outputHandles.Add(new NodeHandle
            {
                Id = "output",
                Name = "Output",
                Type = NodeHandleType.Output,
                Position = "right"
            });

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        /// <summary>
        /// Perform node-specific initialization. Override in derived classes.
        /// </summary>
        protected virtual async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            await Task.CompletedTask;
            return true;
        }

        /// <summary>
        /// Perform node-specific validation. Override in derived classes.
        /// </summary>
        protected virtual async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;
        }

        /// <summary>
        /// Validate node handles.
        /// </summary>
        protected virtual void ValidateHandles(NodeValidationResult result)
        {
            if (InputHandles.Count == 0)
                result.Warnings.Add("Node has no input handles");

            if (OutputHandles.Count == 0)
                result.Warnings.Add("Node has no output handles");
        }

        /// <summary>
        /// Evaluate a rule group against the execution context.
        /// </summary>
        protected async Task<bool> EvaluateRuleGroupAsync(FlowRuleGroup ruleGroup, FlowExecutionContext context)
        {
            var results = new List<bool>();

            // Evaluate individual rules
            foreach (var rule in ruleGroup.Rules)
            {
                var result = await EvaluateRuleAsync(rule, context);
                results.Add(result);
            }

            // Evaluate nested groups
            foreach (var group in ruleGroup.Groups)
            {
                var result = await EvaluateRuleGroupAsync(group, context);
                results.Add(result);
            }

            // Apply combinator logic
            if (ruleGroup.Combinator.ToLower() == "or")
                return results.Any(r => r);
            else // Default to "and"
                return results.All(r => r);
        }

        /// <summary>
        /// Evaluate a single rule against the execution context.
        /// </summary>
        protected async Task<bool> EvaluateRuleAsync(FlowRule rule, FlowExecutionContext context)
        {
            await Task.CompletedTask;

            var fieldValue = context.GetFieldValue(rule.Field);
            if (fieldValue == null && rule.Value != null)
                return false;

            if (fieldValue == null && rule.Value == null)
                return rule.Operator == "=" || rule.Operator == "==";

            return rule.Operator.ToLower() switch
            {
                "=" or "==" => Equals(fieldValue, rule.Value),
                "!=" or "<>" => !Equals(fieldValue, rule.Value),
                "<" => CompareValues(fieldValue, rule.Value) < 0,
                ">" => CompareValues(fieldValue, rule.Value) > 0,
                "<=" => CompareValues(fieldValue, rule.Value) <= 0,
                ">=" => CompareValues(fieldValue, rule.Value) >= 0,
                "contains" => fieldValue?.ToString()?.Contains(rule.Value?.ToString() ?? "") == true,
                "startswith" => fieldValue?.ToString()?.StartsWith(rule.Value?.ToString() ?? "") == true,
                "endswith" => fieldValue?.ToString()?.EndsWith(rule.Value?.ToString() ?? "") == true,
                _ => false
            };
        }

        /// <summary>
        /// Compare two values for ordering operations.
        /// </summary>
        protected int CompareValues(object? left, object? right)
        {
            if (left == null && right == null) return 0;
            if (left == null) return -1;
            if (right == null) return 1;

            if (left is IComparable leftComparable && right is IComparable rightComparable)
            {
                try
                {
                    return leftComparable.CompareTo(rightComparable);
                }
                catch
                {
                    // Fall back to string comparison
                    return string.Compare(left.ToString(), right.ToString(), StringComparison.OrdinalIgnoreCase);
                }
            }

            return string.Compare(left.ToString(), right.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Create a successful execution result.
        /// </summary>
        protected NodeExecutionResult CreateSuccessResult(Dictionary<string, object>? outputData = null, List<string>? nextNodeIds = null)
        {
            return new NodeExecutionResult
            {
                NodeId = NodeId,
                Status = NodeExecutionStatus.Completed,
                OutputData = outputData ?? new(),
                NextNodeIds = nextNodeIds ?? new(),
                CompletedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Create a failed execution result.
        /// </summary>
        protected NodeExecutionResult CreateFailureResult(string errorMessage, Exception? exception = null)
        {
            return new NodeExecutionResult
            {
                NodeId = NodeId,
                Status = NodeExecutionStatus.Failed,
                ErrorMessage = errorMessage,
                Exception = exception,
                CompletedAt = DateTime.UtcNow
            };
        }
    }
}
