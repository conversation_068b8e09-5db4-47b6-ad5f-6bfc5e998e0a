namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Represents the result of executing a MultiStepProcess iteration.
    /// Contains both success status and a generic result object.
    /// </summary>
    /// <typeparam name="TReturn">The type of the result object returned by the task</typeparam>
    public class MultiStepProcessResult<TReturn>
    {
        /// <summary>
        /// Indicates whether the iteration was successful.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The result object returned by the executed task.
        /// </summary>
        public TReturn? Result { get; set; }

        /// <summary>
        /// Initializes a new instance of MultiStepProcessResult.
        /// </summary>
        /// <param name="success">Whether the iteration was successful</param>
        /// <param name="result">The result object from the task</param>
        public MultiStepProcessResult(bool success, TReturn? result)
        {
            Success = success;
            Result = result;
        }

        /// <summary>
        /// Creates a successful result with the specified result object.
        /// </summary>
        /// <param name="result">The result object</param>
        /// <returns>A successful MultiStepProcessResult</returns>
        public static MultiStepProcessResult<TReturn> CreateSuccess(TReturn? result)
        {
            return new MultiStepProcessResult<TReturn>(true, result);
        }

        /// <summary>
        /// Creates a failed result with the specified result object.
        /// </summary>
        /// <param name="result">The result object</param>
        /// <returns>A failed MultiStepProcessResult</returns>
        public static MultiStepProcessResult<TReturn> CreateFailure(TReturn? result)
        {
            return new MultiStepProcessResult<TReturn>(false, result);
        }
    }
}
