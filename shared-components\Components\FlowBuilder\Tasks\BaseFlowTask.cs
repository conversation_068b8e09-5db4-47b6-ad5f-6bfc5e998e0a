using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Tasks
{
    /// <summary>
    /// Base abstract class for all flow tasks providing common functionality.
    /// </summary>
    public abstract class BaseFlowTask : IFlowTask
    {
        public string TaskId { get; protected set; } = string.Empty;
        public string TaskType { get; protected set; } = string.Empty;
        public string Name { get; protected set; } = string.Empty;
        public FlowTaskDefinition Definition { get; protected set; } = new();
        public virtual bool IsLongRunning { get; protected set; } = false;
        public virtual bool SupportsStreaming { get; protected set; } = false;

        public event EventHandler<TaskStreamEventArgs>? OnStreamResult;
        public event EventHandler<TaskStatusChangedEventArgs>? OnStatusChanged;

        protected readonly Dictionary<string, string> _executionSessions = new();

        public virtual async Task<bool> InitializeAsync(FlowTaskDefinition definition)
        {
            Definition = definition;
            TaskId = definition.Id;
            TaskType = definition.Type;
            Name = definition.Name;

            // Perform task-specific initialization
            return await InitializeTaskSpecificAsync(definition);
        }

        public virtual async Task<TaskValidationResult> ValidateAsync(FlowValidationContext context)
        {
            var result = new TaskValidationResult { IsValid = true };

            // Basic validation
            if (string.IsNullOrEmpty(TaskId))
                result.Errors.Add("Task ID cannot be empty");

            if (string.IsNullOrEmpty(Name))
                result.Errors.Add("Task name cannot be empty");

            if (string.IsNullOrEmpty(TaskType))
                result.Errors.Add("Task type cannot be empty");

            // Perform task-specific validation
            await ValidateTaskSpecificAsync(context, result);

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        public abstract Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default);

        public virtual async Task<string> StartLongRunningExecutionAsync(FlowExecutionContext context)
        {
            if (!IsLongRunning)
            {
                throw new InvalidOperationException("This task does not support long-running execution");
            }

            var sessionId = Guid.NewGuid().ToString();
            _executionSessions[sessionId] = "started";

            // Fire status changed event
            OnStatusChanged?.Invoke(this, new TaskStatusChangedEventArgs
            {
                TaskId = TaskId,
                ExecutionSessionId = sessionId,
                OldStatus = TaskExecutionStatus.NotStarted,
                NewStatus = TaskExecutionStatus.LongRunning,
                Message = "Long-running task started"
            });

            // Perform task-specific long-running execution start
            await StartLongRunningTaskSpecificAsync(sessionId, context);

            return sessionId;
        }

        public virtual async Task<TaskExecutionStatus> GetExecutionStatusAsync(string executionSessionId)
        {
            if (!_executionSessions.ContainsKey(executionSessionId))
            {
                return TaskExecutionStatus.NotStarted;
            }

            // Get task-specific status
            return await GetTaskSpecificExecutionStatusAsync(executionSessionId);
        }

        public virtual async Task<bool> CancelExecutionAsync(string executionSessionId)
        {
            if (!_executionSessions.ContainsKey(executionSessionId))
            {
                return false;
            }

            // Perform task-specific cancellation
            var cancelled = await CancelTaskSpecificExecutionAsync(executionSessionId);

            if (cancelled)
            {
                _executionSessions[executionSessionId] = "cancelled";

                // Fire status changed event
                OnStatusChanged?.Invoke(this, new TaskStatusChangedEventArgs
                {
                    TaskId = TaskId,
                    ExecutionSessionId = executionSessionId,
                    OldStatus = TaskExecutionStatus.Running,
                    NewStatus = TaskExecutionStatus.Cancelled,
                    Message = "Task execution cancelled"
                });
            }

            return cancelled;
        }

        public virtual async Task OnExecutionCompletedAsync(TaskExecutionResult result, FlowExecutionContext context)
        {
            // Default implementation - can be overridden for cleanup or logging
            await Task.CompletedTask;
        }

        public virtual async Task OnExecutionCancelledAsync(FlowExecutionContext context)
        {
            // Default implementation - can be overridden for cleanup
            await Task.CompletedTask;
        }

        /// <summary>
        /// Perform task-specific initialization. Override in derived classes.
        /// </summary>
        protected virtual async Task<bool> InitializeTaskSpecificAsync(FlowTaskDefinition definition)
        {
            await Task.CompletedTask;
            return true;
        }

        /// <summary>
        /// Perform task-specific validation. Override in derived classes.
        /// </summary>
        protected virtual async Task ValidateTaskSpecificAsync(FlowValidationContext context, TaskValidationResult result)
        {
            await Task.CompletedTask;
        }

        /// <summary>
        /// Start task-specific long-running execution. Override in derived classes.
        /// </summary>
        protected virtual async Task StartLongRunningTaskSpecificAsync(string sessionId, FlowExecutionContext context)
        {
            await Task.CompletedTask;
        }

        /// <summary>
        /// Get task-specific execution status. Override in derived classes.
        /// </summary>
        protected virtual async Task<TaskExecutionStatus> GetTaskSpecificExecutionStatusAsync(string executionSessionId)
        {
            await Task.CompletedTask;
            var status = _executionSessions.GetValueOrDefault(executionSessionId, "unknown");
            return status switch
            {
                "started" => TaskExecutionStatus.Running,
                "completed" => TaskExecutionStatus.Completed,
                "failed" => TaskExecutionStatus.Failed,
                "cancelled" => TaskExecutionStatus.Cancelled,
                _ => TaskExecutionStatus.NotStarted
            };
        }

        /// <summary>
        /// Cancel task-specific execution. Override in derived classes.
        /// </summary>
        protected virtual async Task<bool> CancelTaskSpecificExecutionAsync(string executionSessionId)
        {
            await Task.CompletedTask;
            return true;
        }

        /// <summary>
        /// Fire a stream result event.
        /// </summary>
        protected void FireStreamResult(string streamType, object data, string? executionSessionId = null)
        {
            if (SupportsStreaming)
            {
                OnStreamResult?.Invoke(this, new TaskStreamEventArgs
                {
                    TaskId = TaskId,
                    ExecutionSessionId = executionSessionId ?? string.Empty,
                    StreamType = streamType,
                    Data = data
                });
            }
        }

        /// <summary>
        /// Fire a status changed event.
        /// </summary>
        protected void FireStatusChanged(TaskExecutionStatus oldStatus, TaskExecutionStatus newStatus, string? message = null, string? executionSessionId = null)
        {
            OnStatusChanged?.Invoke(this, new TaskStatusChangedEventArgs
            {
                TaskId = TaskId,
                ExecutionSessionId = executionSessionId ?? string.Empty,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message
            });
        }

        /// <summary>
        /// Create a successful execution result.
        /// </summary>
        protected TaskExecutionResult CreateSuccessResult(Dictionary<string, object>? outputData = null, bool affectsFlowExecution = false)
        {
            return new TaskExecutionResult
            {
                TaskId = TaskId,
                Status = TaskExecutionStatus.Completed,
                OutputData = outputData ?? new(),
                AffectsFlowExecution = affectsFlowExecution,
                CompletedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Create a failed execution result.
        /// </summary>
        protected TaskExecutionResult CreateFailureResult(string errorMessage, Exception? exception = null)
        {
            return new TaskExecutionResult
            {
                TaskId = TaskId,
                Status = TaskExecutionStatus.Failed,
                ErrorMessage = errorMessage,
                Exception = exception,
                CompletedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Get a configuration value with type conversion.
        /// </summary>
        protected T? GetConfigValue<T>(string key, T? defaultValue = default)
        {
            return Definition.GetConfigValue(key, defaultValue);
        }

        /// <summary>
        /// Set a configuration value.
        /// </summary>
        protected void SetConfigValue(string key, object value)
        {
            Definition.SetConfigValue(key, value);
        }
    }
}
