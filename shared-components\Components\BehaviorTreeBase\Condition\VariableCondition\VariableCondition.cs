﻿using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Condition.VariableCondition
{
    public class VariableCondition<T> : BaseCondition<T>, ICondition where T : IComparable
    {
        [JsonInclude]
        private string variableKey;
        [JsonInclude]
        private T value;
        [JsonInclude]
        private ComparatorType comparator;

        public VariableCondition(string variableKey, T value, ComparatorType comparator)
        {
            this.variableKey = variableKey;
            this.value = value;
            this.comparator = comparator;
        }

        public bool Evaluate(TreeState treeState)
        {
            if (!treeState.Variables.ContainsKey(variableKey)) return false;

            return Compare(treeState.Variables[variableKey], value, comparator);
        }
    }
}
