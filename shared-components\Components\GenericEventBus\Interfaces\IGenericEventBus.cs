using shared.Components.GenericEventBus.Models;

namespace shared.Components.GenericEventBus.Interfaces
{
    /// <summary>
    /// Generic event bus interface for handling events and message queuing across the application.
    /// This is a shared component that can be used by any part of the system, not just FlowBuilder.
    /// </summary>
    public interface IGenericEventBus
    {
        /// <summary>
        /// Sends a message to the event bus for processing.
        /// </summary>
        /// <param name="message">The message to send</param>
        /// <param name="delayInSeconds">Optional delay before processing</param>
        /// <returns>Message ID for tracking</returns>
        Task<string> SendMessageAsync(GenericEventMessage message, int delayInSeconds = 0);

        /// <summary>
        /// Sends a message to a specific queue.
        /// </summary>
        /// <param name="queueName">The target queue name</param>
        /// <param name="message">The message to send</param>
        /// <param name="delayInSeconds">Optional delay before processing</param>
        /// <returns>Message ID for tracking</returns>
        Task<string> SendToQueueAsync(string queueName, GenericEventMessage message, int delayInSeconds = 0);

        /// <summary>
        /// Subscribes to messages of a specific type.
        /// </summary>
        /// <param name="messageType">The message type to subscribe to</param>
        /// <param name="handler">The handler function for processing messages</param>
        void Subscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Subscribes to messages from a specific queue.
        /// </summary>
        /// <param name="queueName">The queue name to subscribe to</param>
        /// <param name="handler">The handler function for processing messages</param>
        void SubscribeToQueue(string queueName, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Unsubscribes from messages of a specific type.
        /// </summary>
        /// <param name="messageType">The message type to unsubscribe from</param>
        /// <param name="handler">The handler function to remove</param>
        void Unsubscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Starts the event bus message processing.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops the event bus message processing.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of a message by its ID.
        /// </summary>
        /// <param name="messageId">The message ID</param>
        /// <returns>Message processing status</returns>
        Task<MessageStatus> GetMessageStatusAsync(string messageId);

        /// <summary>
        /// Event fired when a message is processed successfully.
        /// </summary>
        event EventHandler<MessageProcessedEventArgs>? OnMessageProcessed;

        /// <summary>
        /// Event fired when message processing fails.
        /// </summary>
        event EventHandler<MessageFailedEventArgs>? OnMessageFailed;
    }
}
