using shared.Models.Enums;
using shared.Converters;
using shared.Models.Response;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class KnowledgeBaseFileResponse : DynamoDBModelResponse
    {
        public string FileId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long UploadedFileSize { get; set; }
        public bool IsDeployed { get; set; } = false;

        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseFileUploadStatus>))]
        public KnowledgeBaseFileUploadStatus Status { get; set; }
    }
}
