﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Condition.InputCondition
{
    public class InputCondition<T> : BaseCondition<T>, ICondition where T : IComparable
    {
        public string InputKey { get; set; } = string.Empty;
        public T? Value { get; set; } = default(T);
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<ComparatorType>))]
        [JsonConverter(typeof(JsonEnumStringConverter<ComparatorType>))]
        public ComparatorType Comparator { get; set; } = ComparatorType.Equals;


        /*public InputCondition(string inputKey, T value, ComparatorType comparator)
        {
            this.inputKey = inputKey;
            this.value = value;
            this.comparator = comparator;
            Test = "test";
        }*/

        public bool Evaluate(TreeState treeState)
        {
            if (!treeState.Inputs.ContainsKey(InputKey)) return false;
            if(Value == null) return Comparator == ComparatorType.IsNull;

            return Compare(treeState.Inputs[InputKey], Value, Comparator);
        }
    }
}
