using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Initializer.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using shared.Extension;

namespace Initializer.Services
{
    /// <summary>
    /// Extension methods for configuring services in the DI container.
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds all required services for the Initializer application.
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddInitializerServices(
            this IServiceCollection services, 
            IConfiguration configuration)
        {
            // Add configuration options
            services.Configure<TableManagementOptions>(
                configuration.GetSection(TableManagementOptions.SectionName));

            // Add AWS services
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>();

            // Add DynamoDB table manager (generic, will be used via reflection)
            services.AddDynamoDBWithTableManager();

            // Add custom services
            services.AddScoped<IModelTypeDiscoveryService, ModelTypeDiscoveryService>();
            services.AddScoped<ITableInitializationService, TableInitializationService>();

            return services;
        }
    }
}
