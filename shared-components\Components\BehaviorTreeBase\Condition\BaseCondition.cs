﻿using shared.Components.BehaviorTreeBase.Enum;
using System.Globalization;

namespace shared.Components.BehaviorTree.Condition
{
    public class BaseCondition<T> where T : IComparable
    {

        private static double? IsNumeric(object expression)
        {
            if (expression == null)
                return null;

            double number;
            Double.TryParse(
                Convert.ToString(expression, CultureInfo.InvariantCulture),
                System.Globalization.NumberStyles.Any,
                NumberFormatInfo.InvariantInfo,
                out number);
            return number;
        }

        private static bool CompareSwitch<F>(F storedValue, F comparisonValue, ComparatorType comparator) where F : IComparable
        {
            switch (comparator)
            {
                case ComparatorType.GreaterThan:
                    return comparisonValue.CompareTo(storedValue) > 0;
                case ComparatorType.GreaterThanOrEqual:
                    return comparisonValue.CompareTo(storedValue) >= 0;
                case ComparatorType.LessThan:
                    return comparisonValue.CompareTo(storedValue) < 0;
                case ComparatorType.LessThanOrEqual:
                    return comparisonValue.CompareTo(storedValue) <= 0;
                case ComparatorType.Equals:
                    return comparisonValue.CompareTo(storedValue) == 0;
                case ComparatorType.NotEquals:
                    return comparisonValue.CompareTo(storedValue) != 0;
            }
            return false;
        }

        public static bool Compare(object? storedValue, T comparisonValue, ComparatorType comparator)
        {

            if (comparisonValue == null) return false;
            if (storedValue == null) return false;

            if (comparisonValue.GetType().Equals(storedValue.GetType())) {
                return CompareSwitch((T)storedValue, comparisonValue, comparator);
            }

            double? storedDouble = IsNumeric(storedValue);
            double? comparisonDouble = IsNumeric(comparisonValue);

            if(storedDouble != null && comparisonDouble != null)
            {
                return CompareSwitch<double>((double)storedDouble, (double)comparisonDouble, comparator);
            }

            string storedStr = storedValue.ToString() ?? string.Empty;
            string comparisonStr = comparisonValue.ToString() ?? string.Empty;

            return CompareSwitch<string>(storedStr, comparisonStr, comparator);
        }
    }
}
