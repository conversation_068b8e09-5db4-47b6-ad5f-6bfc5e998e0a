using shared.Components.FlowBuilder.Models;
using shared.Models.Documents.DynamoDB;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace shared.Components.FlowBuilder.FieldSystem
{
    /// <summary>
    /// Manages flow fields including validation, type conversion, and variable access.
    /// Supports the field types specified in the documentation: STRING, INTEGER, FLOAT, BOOLEAN, JSON.
    /// </summary>
    public class FlowFieldManager
    {
        private readonly Dictionary<string, FlowField> _fieldDefinitions;
        private readonly Dictionary<string, object> _fieldValues;
        private readonly Dictionary<string, object> _inputFields;
        private readonly HashSet<string> _readOnlyFields;

        public FlowFieldManager(List<FlowField> fieldDefinitions, Dictionary<string, object> inputFields)
        {
            _fieldDefinitions = fieldDefinitions.ToDictionary(f => f.Name, f => f);
            _fieldValues = new Dictionary<string, object>();
            _inputFields = new Dictionary<string, object>(inputFields);
            _readOnlyFields = new HashSet<string>();

            // Mark input fields as read-only
            foreach (var inputField in inputFields.Keys)
            {
                _readOnlyFields.Add(inputField);
            }

            // Initialize field values with defaults
            InitializeFieldValues();
        }

        /// <summary>
        /// Get the value of a field (input or variable).
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <returns>The field value or null if not found</returns>
        public object? GetFieldValue(string fieldName)
        {
            // Check input fields first
            if (_inputFields.ContainsKey(fieldName))
                return _inputFields[fieldName];

            // Check variable fields
            if (_fieldValues.ContainsKey(fieldName))
                return _fieldValues[fieldName];

            return null;
        }

        /// <summary>
        /// Get a typed field value with conversion.
        /// </summary>
        /// <typeparam name="T">The target type</typeparam>
        /// <param name="fieldName">The field name</param>
        /// <param name="defaultValue">Default value if field not found or conversion fails</param>
        /// <returns>The converted field value</returns>
        public T GetFieldValue<T>(string fieldName, T defaultValue = default!)
        {
            var value = GetFieldValue(fieldName);
            if (value == null)
                return defaultValue;

            try
            {
                return ConvertValue<T>(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Set the value of a variable field (input fields cannot be changed).
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <param name="value">The field value</param>
        /// <returns>True if the field was set successfully</returns>
        public bool SetFieldValue(string fieldName, object value)
        {
            // Cannot modify input fields
            if (_readOnlyFields.Contains(fieldName))
                return false;

            // Validate field definition exists
            if (!_fieldDefinitions.ContainsKey(fieldName))
            {
                // Allow dynamic fields for runtime variables
                _fieldValues[fieldName] = value;
                return true;
            }

            var fieldDef = _fieldDefinitions[fieldName];

            // Validate and convert value
            var validationResult = ValidateAndConvertValue(fieldDef, value);
            if (!validationResult.IsValid)
                return false;

            _fieldValues[fieldName] = validationResult.ConvertedValue!;
            return true;
        }

        /// <summary>
        /// Validate a field value against its definition.
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <param name="value">The value to validate</param>
        /// <returns>Validation result</returns>
        public FieldValidationResult ValidateFieldValue(string fieldName, object value)
        {
            if (!_fieldDefinitions.ContainsKey(fieldName))
            {
                return new FieldValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Field '{fieldName}' is not defined"
                };
            }

            var fieldDef = _fieldDefinitions[fieldName];
            return ValidateAndConvertValue(fieldDef, value);
        }

        /// <summary>
        /// Get all field values as a dictionary.
        /// </summary>
        /// <returns>Dictionary containing all field values</returns>
        public Dictionary<string, object> GetAllFieldValues()
        {
            var allFields = new Dictionary<string, object>();

            // Add input fields
            foreach (var kvp in _inputFields)
                allFields[kvp.Key] = kvp.Value;

            // Add variable fields
            foreach (var kvp in _fieldValues)
                allFields[kvp.Key] = kvp.Value;

            return allFields;
        }

        /// <summary>
        /// Get only variable field values (excluding input fields).
        /// </summary>
        /// <returns>Dictionary containing variable field values</returns>
        public Dictionary<string, object> GetVariableFieldValues()
        {
            return new Dictionary<string, object>(_fieldValues);
        }

        /// <summary>
        /// Check if a field exists.
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <returns>True if the field exists</returns>
        public bool FieldExists(string fieldName)
        {
            return _inputFields.ContainsKey(fieldName) || _fieldValues.ContainsKey(fieldName);
        }

        /// <summary>
        /// Check if a field is read-only (input field).
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <returns>True if the field is read-only</returns>
        public bool IsFieldReadOnly(string fieldName)
        {
            return _readOnlyFields.Contains(fieldName);
        }

        /// <summary>
        /// Get field definition.
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <returns>Field definition or null if not found</returns>
        public FlowField? GetFieldDefinition(string fieldName)
        {
            return _fieldDefinitions.GetValueOrDefault(fieldName);
        }

        /// <summary>
        /// Substitute variables in a string using {fieldName} syntax.
        /// </summary>
        /// <param name="template">The template string</param>
        /// <returns>String with variables substituted</returns>
        public string SubstituteVariables(string template)
        {
            if (string.IsNullOrEmpty(template))
                return template;

            var result = template;
            var regex = new Regex(@"\{([^}]+)\}", RegexOptions.Compiled);

            var matches = regex.Matches(template);
            foreach (Match match in matches)
            {
                var fieldName = match.Groups[1].Value;
                var fieldValue = GetFieldValue(fieldName);
                var stringValue = fieldValue?.ToString() ?? "";
                result = result.Replace(match.Value, stringValue);
            }

            return result;
        }

        /// <summary>
        /// Initialize field values with defaults.
        /// </summary>
        private void InitializeFieldValues()
        {
            foreach (var fieldDef in _fieldDefinitions.Values)
            {
                // Skip input fields as they are already set
                if (fieldDef.Type == FlowFieldType.Input)
                    continue;

                // Set default value if specified and field not already set
                if (!string.IsNullOrEmpty(fieldDef.DefaultValue) && !_fieldValues.ContainsKey(fieldDef.Name))
                {
                    var defaultValue = ConvertStringToDataType(fieldDef.DefaultValue, fieldDef.DataType);
                    if (defaultValue != null)
                    {
                        _fieldValues[fieldDef.Name] = defaultValue;
                    }
                }
            }
        }

        /// <summary>
        /// Validate and convert a value according to field definition.
        /// </summary>
        /// <param name="fieldDef">The field definition</param>
        /// <param name="value">The value to validate and convert</param>
        /// <returns>Validation result with converted value</returns>
        private FieldValidationResult ValidateAndConvertValue(FlowField fieldDef, object value)
        {
            // Check required fields
            if (fieldDef.Required && (value == null || (value is string str && string.IsNullOrEmpty(str))))
            {
                return new FieldValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Field '{fieldDef.Name}' is required"
                };
            }

            // Allow null for non-required fields
            if (value == null && !fieldDef.Required)
            {
                return new FieldValidationResult
                {
                    IsValid = true,
                    ConvertedValue = null
                };
            }

            // Convert and validate based on data type
            try
            {
                var convertedValue = ConvertValueToDataType(value!, fieldDef.DataType);
                return new FieldValidationResult
                {
                    IsValid = true,
                    ConvertedValue = convertedValue
                };
            }
            catch (Exception ex)
            {
                return new FieldValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Invalid value for field '{fieldDef.Name}': {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Convert a value to the specified data type.
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="dataType">The target data type</param>
        /// <returns>The converted value</returns>
        private object ConvertValueToDataType(object value, FlowDataType dataType)
        {
            return dataType switch
            {
                FlowDataType.STRING => value.ToString() ?? "",
                FlowDataType.INTEGER => Convert.ToInt32(value),
                FlowDataType.FLOAT => Convert.ToDouble(value),
                FlowDataType.BOOLEAN => Convert.ToBoolean(value),
                FlowDataType.JSON => ConvertToJson(value),
                _ => throw new ArgumentException($"Unsupported data type: {dataType}")
            };
        }

        /// <summary>
        /// Convert a string default value to the specified data type.
        /// </summary>
        /// <param name="stringValue">The string value</param>
        /// <param name="dataType">The target data type</param>
        /// <returns>The converted value or null if conversion fails</returns>
        private object? ConvertStringToDataType(string stringValue, FlowDataType dataType)
        {
            try
            {
                return dataType switch
                {
                    FlowDataType.STRING => stringValue,
                    FlowDataType.INTEGER => int.Parse(stringValue),
                    FlowDataType.FLOAT => double.Parse(stringValue),
                    FlowDataType.BOOLEAN => bool.Parse(stringValue),
                    FlowDataType.JSON => JsonSerializer.Deserialize<object>(stringValue),
                    _ => null
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Convert a value to JSON format.
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <returns>JSON object or the original value if already JSON-compatible</returns>
        private object ConvertToJson(object value)
        {
            if (value is string jsonString)
            {
                try
                {
                    return JsonSerializer.Deserialize<object>(jsonString) ?? value;
                }
                catch
                {
                    return value;
                }
            }

            return value;
        }

        /// <summary>
        /// Convert a value to a specific type.
        /// </summary>
        /// <typeparam name="T">The target type</typeparam>
        /// <param name="value">The value to convert</param>
        /// <returns>The converted value</returns>
        private T ConvertValue<T>(object value)
        {
            if (value is T directValue)
                return directValue;

            if (typeof(T) == typeof(string))
                return (T)(object)(value.ToString() ?? "");

            return (T)Convert.ChangeType(value, typeof(T));
        }
    }

    /// <summary>
    /// Result of field validation.
    /// </summary>
    public class FieldValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public object? ConvertedValue { get; set; }
    }
}
