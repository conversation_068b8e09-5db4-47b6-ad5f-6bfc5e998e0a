namespace shared.Components.MessageBus.Configuration
{
    /// <summary>
    /// Retry policy configuration for message processing
    /// </summary>
    public class RetryPolicy
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Base delay in seconds for exponential backoff
        /// </summary>
        public int BaseDelaySeconds { get; set; } = 1;

        /// <summary>
        /// Maximum delay in seconds
        /// </summary>
        public int MaxDelaySeconds { get; set; } = 300; // 5 minutes

        /// <summary>
        /// Multiplier for exponential backoff
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// Whether to add jitter to prevent thundering herd
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// Jitter factor (0.0 to 1.0)
        /// </summary>
        public double JitterFactor { get; set; } = 0.1;

        /// <summary>
        /// Calculate delay for a specific retry attempt
        /// </summary>
        /// <param name="retryCount">Current retry count (1-based)</param>
        /// <returns>Delay in seconds</returns>
        public int CalculateDelay(int retryCount)
        {
            if (retryCount <= 0)
                return 0;

            // Calculate exponential backoff
            var delay = BaseDelaySeconds * Math.Pow(BackoffMultiplier, retryCount - 1);
            
            // Apply maximum delay limit
            delay = Math.Min(delay, MaxDelaySeconds);

            // Add jitter if enabled
            if (UseJitter)
            {
                var jitter = delay * JitterFactor * (Random.Shared.NextDouble() * 2 - 1); // -JitterFactor to +JitterFactor
                delay += jitter;
            }

            return Math.Max(1, (int)Math.Round(delay));
        }

        /// <summary>
        /// Check if retry is allowed for the given attempt count
        /// </summary>
        /// <param name="retryCount">Current retry count</param>
        /// <returns>True if retry is allowed</returns>
        public bool ShouldRetry(int retryCount)
        {
            return retryCount < MaxRetries;
        }

        /// <summary>
        /// Default retry policy
        /// </summary>
        public static RetryPolicy Default => new RetryPolicy();

        /// <summary>
        /// Aggressive retry policy for critical messages
        /// </summary>
        public static RetryPolicy Aggressive => new RetryPolicy
        {
            MaxRetries = 5,
            BaseDelaySeconds = 1,
            MaxDelaySeconds = 60,
            BackoffMultiplier = 1.5,
            UseJitter = true,
            JitterFactor = 0.2
        };

        /// <summary>
        /// Conservative retry policy for non-critical messages
        /// </summary>
        public static RetryPolicy Conservative => new RetryPolicy
        {
            MaxRetries = 2,
            BaseDelaySeconds = 5,
            MaxDelaySeconds = 600,
            BackoffMultiplier = 3.0,
            UseJitter = true,
            JitterFactor = 0.1
        };

        /// <summary>
        /// No retry policy
        /// </summary>
        public static RetryPolicy None => new RetryPolicy
        {
            MaxRetries = 0
        };
    }

    /// <summary>
    /// Retry context for tracking retry attempts
    /// </summary>
    public class RetryContext
    {
        /// <summary>
        /// Message being retried
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// Current retry attempt (1-based)
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Maximum retries allowed
        /// </summary>
        public int MaxRetries { get; set; }

        /// <summary>
        /// Last error that occurred
        /// </summary>
        public string? LastError { get; set; }

        /// <summary>
        /// Timestamp of last retry attempt
        /// </summary>
        public DateTime LastRetryAt { get; set; }

        /// <summary>
        /// Timestamp when message was first created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Additional context data
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Check if more retries are allowed
        /// </summary>
        public bool CanRetry => RetryCount < MaxRetries;

        /// <summary>
        /// Get total time elapsed since creation
        /// </summary>
        public TimeSpan TotalElapsed => DateTime.UtcNow - CreatedAt;

        /// <summary>
        /// Get time since last retry
        /// </summary>
        public TimeSpan TimeSinceLastRetry => DateTime.UtcNow - LastRetryAt;
    }

    /// <summary>
    /// Exception types that should trigger retries
    /// </summary>
    public static class RetryableExceptions
    {
        /// <summary>
        /// Check if an exception should trigger a retry
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if the exception is retryable</returns>
        public static bool IsRetryable(Exception exception)
        {
            return exception switch
            {
                TimeoutException => true,
                HttpRequestException => true,
                TaskCanceledException => true,
                OperationCanceledException => false, // Don't retry cancellations
                ArgumentException => false, // Don't retry argument errors
                InvalidOperationException => false, // Don't retry invalid operations
                _ => IsTransientException(exception)
            };
        }

        /// <summary>
        /// Check if an exception appears to be transient
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if the exception appears transient</returns>
        private static bool IsTransientException(Exception exception)
        {
            var message = exception.Message.ToLowerInvariant();
            
            // Common transient error patterns
            var transientPatterns = new[]
            {
                "timeout",
                "connection",
                "network",
                "temporary",
                "unavailable",
                "throttle",
                "rate limit",
                "service unavailable",
                "internal server error"
            };

            return transientPatterns.Any(pattern => message.Contains(pattern));
        }
    }

    /// <summary>
    /// Circuit breaker for preventing cascading failures
    /// </summary>
    public class CircuitBreaker
    {
        private readonly object _lock = new object();
        private int _failureCount = 0;
        private DateTime _lastFailureTime = DateTime.MinValue;
        private CircuitBreakerState _state = CircuitBreakerState.Closed;

        /// <summary>
        /// Failure threshold before opening circuit
        /// </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary>
        /// Time to wait before attempting to close circuit
        /// </summary>
        public TimeSpan RecoveryTimeout { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Current state of the circuit breaker
        /// </summary>
        public CircuitBreakerState State
        {
            get
            {
                lock (_lock)
                {
                    if (_state == CircuitBreakerState.Open && 
                        DateTime.UtcNow - _lastFailureTime >= RecoveryTimeout)
                    {
                        _state = CircuitBreakerState.HalfOpen;
                    }
                    return _state;
                }
            }
        }

        /// <summary>
        /// Check if operation should be allowed
        /// </summary>
        /// <returns>True if operation is allowed</returns>
        public bool ShouldAllowOperation()
        {
            return State != CircuitBreakerState.Open;
        }

        /// <summary>
        /// Record a successful operation
        /// </summary>
        public void RecordSuccess()
        {
            lock (_lock)
            {
                _failureCount = 0;
                _state = CircuitBreakerState.Closed;
            }
        }

        /// <summary>
        /// Record a failed operation
        /// </summary>
        public void RecordFailure()
        {
            lock (_lock)
            {
                _failureCount++;
                _lastFailureTime = DateTime.UtcNow;

                if (_failureCount >= FailureThreshold)
                {
                    _state = CircuitBreakerState.Open;
                }
            }
        }
    }

    /// <summary>
    /// Circuit breaker states
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary>
        /// Normal operation
        /// </summary>
        Closed,

        /// <summary>
        /// Blocking operations due to failures
        /// </summary>
        Open,

        /// <summary>
        /// Testing if service has recovered
        /// </summary>
        HalfOpen
    }
}
