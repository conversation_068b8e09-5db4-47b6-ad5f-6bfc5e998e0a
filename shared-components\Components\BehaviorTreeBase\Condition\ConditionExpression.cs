﻿using System.Text.Json.Serialization;
using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTreeBase.Models;
using shared.Converters;

namespace shared.Components.BehaviorTree.Condition
{
    public class ConditionExpression : ICondition
    {
        public List<ICondition> Conditions { get; set; } = new List<ICondition>();

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<ConditionOperation>))]
        [JsonConverter(typeof(JsonEnumStringConverter<ConditionOperation>))]
        public ConditionOperation Operation { get; set; } = ConditionOperation.NONE;
        public bool Not { get; set; } = false;

        public ConditionExpression() { }

        public ConditionExpression(List<ICondition> conditions, ConditionOperation operation, bool not)
        {
            this.Conditions = conditions;
            this.Operation = operation;
            this.Not = not;
        }

        public bool Evaluate(TreeState treeState)
        {
            bool one_true = false;
            foreach (ICondition condition in Conditions)
            {
                switch (condition.Evaluate(treeState))
                {
                    case false:
                        if (Operation == ConditionOperation.AND) return Not;
                        continue;
                    case true:
                        one_true = true;
                        continue;
                }

            }
            if (Not) return !one_true;
            return one_true;
        }
    }
}
