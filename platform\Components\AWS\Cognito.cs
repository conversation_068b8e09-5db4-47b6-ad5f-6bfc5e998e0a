﻿using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using Amazon.Extensions.CognitoAuthentication;
using Amazon.Runtime.Internal.Transform;
using Microsoft.Extensions.Options;
using platform.Configuration;
using platform.Models.Response;
using shared.Models.Configuration;
using shared.Services;

namespace platform.Components.AWS
{
    public class Cognito
    {
        public class CreateUserResponse
        {
            public string UserId { get; set; } = string.Empty;
            public string UserSub { get; set; } = string.Empty;
        }

        private readonly IAmazonCognitoIdentityProvider identityProvider;
        private readonly ISecretsService secretsService;
        private readonly IOptionsMonitor<UserPoolConfiguration> userPoolConfiguration;
        private readonly IOptions<MicroserviceConfiguration> microserviceConfiguration;



        public Cognito(IAmazonCognitoIdentityProvider identityProvider, IOptions<MicroserviceConfiguration> microserviceConfiguration, IOptionsMonitor<UserPoolConfiguration> userPoolConfiguration, ISecretsService secretsService)
        {
            this.identityProvider = identityProvider;
            this.secretsService = secretsService;
            this.microserviceConfiguration = microserviceConfiguration;
            this.userPoolConfiguration = userPoolConfiguration;
        }

        private async Task<string> GetSecretHash(string username)
        {
            return shared.Helpers.EncodingHelper.Base64Encode(
                    shared.Helpers.EncodingHelper.HMACSHA256Encode(
                        username + userPoolConfiguration.CurrentValue.ClientId,
                        await secretsService.Get(microserviceConfiguration.Value.Environment, userPoolConfiguration.CurrentValue.ClientSecret.SecretName)
                    )
                );
        }

        public async Task<bool> UpdateUserAttributes(string username, Dictionary<string, string> attributes)
        {
            string clientSecret = await secretsService.Get(microserviceConfiguration.Value.Environment, userPoolConfiguration.CurrentValue.ClientSecret.SecretName);

            var cognitoUserPool = new CognitoUserPool(
                userPoolConfiguration.CurrentValue.UserPoolId,
                userPoolConfiguration.CurrentValue.ClientId,
                identityProvider
            );

            var cognitoUser = new CognitoUser(username, userPoolConfiguration.CurrentValue.ClientId, cognitoUserPool, identityProvider, clientSecret);

            try
            {
                await cognitoUser.UpdateAttributesAsync(attributes);
                return true;
            }
            catch (Exception ex) {
                return false;
            }
        }

        public async Task<CreateUserResponse> CreateUser(string email, string password, Dictionary<string, string> attributes, string? userId = null)
        {
            if (userId == null) userId = Guid.NewGuid().ToString();

            var userSignUpRequest = new SignUpRequest
            {
                ClientId = userPoolConfiguration.CurrentValue.ClientId,
                SecretHash = await GetSecretHash(email),
                Password = password,
                Username = email
            };


            var attributeUserId = new AttributeType
            {
                Name = "custom:UserId",
                Value = userId,
            };

            userSignUpRequest.UserAttributes.Add(attributeUserId);

            foreach (var attribute in attributes)
            {
                var actualAttribute = new AttributeType
                {
                    Name = attribute.Key,
                    Value = attribute.Value,
                };

                userSignUpRequest.UserAttributes.Add(actualAttribute);
            }

            var userCreated = await identityProvider.SignUpAsync(userSignUpRequest);
            CreateUserResponse createUserResponse = new CreateUserResponse { UserId = userId, UserSub = userCreated.UserSub };
            return createUserResponse;
        }



        public async Task<bool> ConfirmEmail(string email, string confirmationCode)
        {
            try
            {
                var confirmSignUpRequest = new ConfirmSignUpRequest
                {
                    Username = email,
                    ClientId = userPoolConfiguration.CurrentValue.ClientId,
                    ConfirmationCode = confirmationCode,
                    SecretHash = await GetSecretHash(email)
                };

                var ret = await identityProvider.ConfirmSignUpAsync(confirmSignUpRequest);
            }
            catch (Exception e)
            {
                Console.WriteLine($"{e.Message}");
                return false;
            }
            return true;
        }


        /*public async Task<SigninResponse> LoginContinue(SigninResponse lastResponse)
        {
            return 
        }*/

        public async Task<SigninResponse> Login(string username, string password)
        {

            try
            {
                string clientSecret = await secretsService.Get(microserviceConfiguration.Value.Environment, userPoolConfiguration.CurrentValue.ClientSecret.SecretName);

                var cognitoUserPool = new CognitoUserPool(
                    userPoolConfiguration.CurrentValue.UserPoolId,
                    userPoolConfiguration.CurrentValue.ClientId,
                    identityProvider
                );

                var cognitoUser = new CognitoUser(username, userPoolConfiguration.CurrentValue.ClientId, cognitoUserPool, identityProvider, clientSecret);

                var authResponse = await cognitoUser.StartWithSrpAuthAsync(new InitiateSrpAuthRequest { Password = password }).ConfigureAwait(false);

                if (authResponse.AuthenticationResult == null)
                {

                    if (authResponse.ChallengeName == ChallengeNameType.NEW_PASSWORD_REQUIRED)
                    {
                        return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.NEW_PASSWORD_REQUIRED, SessionId = authResponse.SessionID };
                    }
                    else if (authResponse.ChallengeName == ChallengeNameType.SMS_MFA)
                    {
                        return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.MFA_REQUIRED, SessionId = authResponse.SessionID };
                    }
                }

                var userResponse = await cognitoUser.GetUserDetailsAsync();
                string userId = userResponse.UserAttributes.First(attr => attr.Name == "custom:UserId").Value;
                string accountId = userResponse.UserAttributes.First(attr => attr.Name == "custom:AccountId").Value;

                return new SigninResponse() { RefreshToken=authResponse.AuthenticationResult.RefreshToken, AccessToken = authResponse.AuthenticationResult.AccessToken, Status = shared.Models.Enums.SigninStatus.LOGIN_SUCCESSFUL, UserId = userId, SessionId = authResponse.SessionID, AccountId = accountId, RefreshTimeout = userPoolConfiguration.CurrentValue.LoginExpireTimeout / 2 };
            }
            catch(NotAuthorizedException ex)
            {
                return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.CREDENTIALS_NOT_FOUND };
            }
            catch (UserNotConfirmedException ex)
            {
                return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.EMAIL_NOT_CONFIRMED };
            }
            catch (UserNotFoundException ex)
            {
                return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.CREDENTIALS_NOT_FOUND };
            }
            catch (Exception e)
            {
                Console.WriteLine($"{e.Message}");
                return new SigninResponse() { Status = shared.Models.Enums.SigninStatus.INTERNAL_ERROR };
            }
        }

        public async Task<bool> ResendConfirmationEmail(string email)
        {
            string clientSecret = await secretsService.Get(microserviceConfiguration.Value.Environment, userPoolConfiguration.CurrentValue.ClientSecret.SecretName);

            var cognitoUserPool = new CognitoUserPool(
                userPoolConfiguration.CurrentValue.UserPoolId,
                userPoolConfiguration.CurrentValue.ClientId,
                identityProvider
            );

            var cognitoUser = new CognitoUser(email, userPoolConfiguration.CurrentValue.ClientId, cognitoUserPool, identityProvider, clientSecret);

            try
            {
                await cognitoUser.ResendConfirmationCodeAsync();
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
