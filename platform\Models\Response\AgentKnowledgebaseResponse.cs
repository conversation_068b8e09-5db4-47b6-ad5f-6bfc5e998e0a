using shared.Models.Enums;
using shared.Converters;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class AgentKnowledgebaseResponse
    {
        public string KbId { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;

        [JsonConverter(typeof(JsonEnumStringConverter<AgentKnowledgebaseOperation>))]
        public AgentKnowledgebaseOperation operation { get; set; } = AgentKnowledgebaseOperation.ASSIGN;
    }
}
