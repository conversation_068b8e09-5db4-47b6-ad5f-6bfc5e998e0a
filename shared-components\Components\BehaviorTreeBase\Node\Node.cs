﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Tasks;
using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTreeBase.Condition.InputCondition;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Tasks;
using shared.Converters;
using System.Linq.Expressions;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTree.Node
{
    [JsonDerivedType(typeof(Selector), typeDiscriminator: nameof(Selector))]
    [JsonDerivedType(typeof(Sequence), typeDiscriminator: nameof(Sequence))]
    [JsonDerivedType(typeof(SetAgentTask), typeDiscriminator: nameof(SetAgentTask))]
    [JsonDerivedType(typeof(SetPromptTask), typeDiscriminator: nameof(SetPromptTask))]
    [JsonDerivedType(typeof(AWSLambdaTask), typeDiscriminator: nameof(AWSLambdaTask))]
    public abstract class Node
    {
        protected Node? parent = null;
        public List<Node> Children { get; set; } = new List<Node>();
        public ConditionExpression? ConditionExpression { get; set; } = null;
        public string NodeId { get; set; } = string.Empty;

        public Node(string nodeId, List<Node>? children, ConditionExpression? conditionExpression)
        {
            this.NodeId = nodeId;
            this.ConditionExpression = conditionExpression;

            if (children != null)
            {
                foreach (Node child in children)
                    _Attach(child);
            }
        }

        private void _Attach(Node node)
        {
            node.parent = this;
            Children.Add(node);
        }

        public bool CheckConditions(TreeState treeState)
        {
            return (ConditionExpression != null && ConditionExpression.Evaluate(treeState)) || ConditionExpression == null;
        }

        public async Task<NodeState> Evaluate(TreeState treeState)
        {
            NodeState state;

            if (!CheckConditions(treeState))
            {
                state = NodeState.FAILURE;
            }
            else
            {
                state = await EvaluateImpl(treeState);
            }

            
            treeState.GetNodeData(NodeId).State = state;
            return state;
        }

        protected abstract Task<NodeState> EvaluateImpl(TreeState treeState);
        
    }
}
