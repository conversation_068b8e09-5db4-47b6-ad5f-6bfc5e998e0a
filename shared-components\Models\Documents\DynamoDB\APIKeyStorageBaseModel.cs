﻿using Amazon.DynamoDBv2.DataModel;
using shared.Converters;
using System.Security.Claims;
using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    public class APIKeyStorageDynamoDBModel : DynamoDBModel
    {
        public const string AccountIdKeySecondaryGlobalIndex = "AccountId-Key-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdKeySecondaryGlobalIndex)]
        public string Key { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Secret { get; set; } = string.Empty;
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdKeySecondaryGlobalIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoDBListOfConverter<Claim, DynamoDBClaimConverter>))]
        [JsonConverter(typeof(JsonListOfConverter<Claim, JsonClaimConverter>))]
        public List<Claim> Claims { get; set; } = new List<Claim>();
        public long ExpireAt { get; set; } = -1;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(APIKeyStorageDynamoDBModel.Key);
            }
            else if (indexName == AccountIdKeySecondaryGlobalIndex)
            {
                // AccountId-Key-index hash key
                return nameof(APIKeyStorageDynamoDBModel.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(APIKeyStorageDynamoDBModel.Secret);
            }
            else if (indexName == AccountIdKeySecondaryGlobalIndex)
            {
                // AccountId-Key-index range key
                return nameof(APIKeyStorageDynamoDBModel.Key);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// This base class doesn't have a table name - derived classes should override this method.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            throw new NotImplementedException("GetTableName must be implemented in derived classes that have DynamoDBTable attribute");
        }
    }
}
