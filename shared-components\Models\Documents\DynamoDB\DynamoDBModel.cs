using Amazon.DynamoDBv2.DataModel;
using shared.Models.Documents;
using System.Reflection;

namespace shared.Models.Documents.DynamoDB
{
    /// <summary>
    /// DynamoDB-specific model class that extends NoSQLModel with DynamoDB attribute-based key resolution.
    /// Uses reflection to automatically discover hash and range keys based on DynamoDB attributes.
    /// </summary>
    public abstract class DynamoDBModel : NoSQLModel
    {
        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// Uses reflection to find properties with DynamoDBHashKey or DynamoDBGlobalSecondaryIndexHashKey attributes.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            // This method should be overridden in derived classes to provide the correct type
            throw new NotImplementedException("GetHashKeyPropertyName must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the hash key property name for a specific type.
        /// </summary>
        /// <param name="modelType">Type to analyze</param>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static string? GetHashKeyPropertyName(Type modelType, string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                var property = GetPropertyByAttribute(modelType, typeof(DynamoDBHashKeyAttribute));
                return property?.Name;
            }
            else
            {
                // Secondary index hash key
                var properties = modelType.GetProperties();
                foreach (var prop in properties)
                {
                    var gsiHashAttr = prop.GetCustomAttributes<DynamoDBGlobalSecondaryIndexHashKeyAttribute>()
                        .FirstOrDefault(attr => attr.IndexNames?.Contains(indexName) == true);
                    if (gsiHashAttr != null)
                        return prop.Name;

                    var lsiHashAttr = prop.GetCustomAttributes<DynamoDBLocalSecondaryIndexRangeKeyAttribute>()
                        .FirstOrDefault(attr => attr.IndexNames?.Contains(indexName) == true);
                    if (lsiHashAttr != null)
                    {
                        // For LSI, hash key is the same as table hash key
                        var tableHashProperty = GetPropertyByAttribute(modelType, typeof(DynamoDBHashKeyAttribute));
                        return tableHashProperty?.Name;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// Uses reflection to find properties with DynamoDBRangeKey or secondary index range key attributes.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            // This method should be overridden in derived classes to provide the correct type
            throw new NotImplementedException("GetRangeKeyPropertyName must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the range key property name for a specific type.
        /// </summary>
        /// <param name="modelType">Type to analyze</param>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static string? GetRangeKeyPropertyName(Type modelType, string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                var property = GetPropertyByAttribute(modelType, typeof(DynamoDBRangeKeyAttribute));
                return property?.Name;
            }
            else
            {
                // Secondary index range key
                var properties = modelType.GetProperties();
                foreach (var prop in properties)
                {
                    var gsiRangeAttr = prop.GetCustomAttributes<DynamoDBGlobalSecondaryIndexRangeKeyAttribute>()
                        .FirstOrDefault(attr => attr.IndexNames?.Contains(indexName) == true);
                    if (gsiRangeAttr != null)
                        return prop.Name;

                    var lsiRangeAttr = prop.GetCustomAttributes<DynamoDBLocalSecondaryIndexRangeKeyAttribute>()
                        .FirstOrDefault(attr => attr.IndexNames?.Contains(indexName) == true);
                    if (lsiRangeAttr != null)
                        return prop.Name;
                }
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            // This method should be overridden in derived classes to provide the correct type
            throw new NotImplementedException("GetTableName must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the table name for a specific type.
        /// </summary>
        /// <param name="modelType">Type to analyze</param>
        /// <returns>Table name</returns>
        public static string GetTableName(Type modelType)
        {
            var tableAttr = modelType.GetCustomAttribute<DynamoDBTableAttribute>();
            return tableAttr?.TableName ?? modelType.Name;
        }

        /// <summary>
        /// Gets the hash key value from the model instance.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key value</returns>
        public override string? GetHashKeyValue(string? indexName = null)
        {
            var propertyName = GetHashKeyPropertyName(GetType(), indexName);
            if (propertyName == null) return null;
            
            return GetPropertyValue(propertyName)?.ToString();
        }

        /// <summary>
        /// Gets the range key value from the model instance.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key value</returns>
        public override string? GetRangeKeyValue(string? indexName = null)
        {
            var propertyName = GetRangeKeyPropertyName(GetType(), indexName);
            if (propertyName == null) return null;
            
            return GetPropertyValue(propertyName)?.ToString();
        }

        /// <summary>
        /// Gets DynamoDB converter type for a property.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Converter type or null</returns>
        public Type? GetDynamoDBConverter(string propertyName)
        {
            PropertyInfo? pInfo = GetType().GetProperty(propertyName);
            DynamoDBPropertyAttribute? attr = pInfo?.GetCustomAttribute<DynamoDBPropertyAttribute>();
            return attr?.Converter;
        }

        /// <summary>
        /// Indexer for dynamic property access.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Property value</returns>
        [DynamoDBIgnore]
        public object? this[string propertyName]
        {
            get => GetPropertyValue(propertyName);
            set => SetPropertyValue(propertyName, value);
        }
    }
}
