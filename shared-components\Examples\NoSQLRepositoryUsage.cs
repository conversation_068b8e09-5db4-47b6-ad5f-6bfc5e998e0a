using Amazon.DynamoDBv2.DataModel;
using shared.Extension;
using shared.Models.Documents.DynamoDB;
using shared.Services;

namespace shared.Examples
{
    /// <summary>
    /// Example usage of the NoSQL abstraction layer.
    /// This file demonstrates how to use the new repository pattern with DynamoDB.
    /// </summary>
    public class NoSQLRepositoryUsage
    {
        /// <summary>
        /// Example model that extends DynamoDBModel (which now extends DynamoDBModel).
        /// </summary>
        [DynamoDBTable("ExampleUser")]
        public class ExampleUser : DynamoDBModel
        {
            public const string AccountIdUserIdIndex = "AccountId-UserId-index";

            [DynamoDBHashKey]
            [DynamoDBGlobalSecondaryIndexHashKey(AccountIdUserIdIndex)]
            public string AccountId { get; set; } = string.Empty;

            [DynamoDBRangeKey]
            [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdUserIdIndex)]
            public string UserId { get; set; } = string.Empty;

            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;

            /// <summary>
            /// Override static methods to provide type-specific implementations.
            /// </summary>
            public static new string? GetHashKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetHashKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string? GetRangeKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetRangeKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string GetTableName()
            {
                return DynamoDBModel.GetTableName(typeof(ExampleUser));
            }

            public override string GetSearchString()
            {
                return $"{Name} {Email}".ToLower();
            }
        }

        /// <summary>
        /// Example service that uses the NoSQL repository for data operations.
        /// Table management is handled separately by ExampleTableManagementService.
        /// </summary>
        public class ExampleUserService
        {
            private readonly INoSQLRepository<ExampleUser> _userRepository;

            public ExampleUserService(INoSQLRepository<ExampleUser> userRepository)
            {
                _userRepository = userRepository;
            }

            /// <summary>
            /// Creates a new user.
            /// </summary>
            public async Task<ExampleUser?> CreateUserAsync(string accountId, string userId, string name, string email)
            {
                var user = new ExampleUser
                {
                    AccountId = accountId,
                    UserId = userId,
                    Name = name,
                    Email = email
                };

                return await _userRepository.PutAsync(user);
            }

            /// <summary>
            /// Gets a user by account ID and user ID.
            /// </summary>
            public async Task<ExampleUser?> GetUserAsync(string accountId, string userId)
            {
                return await _userRepository.GetAsync(accountId, userId);
            }

            /// <summary>
            /// Gets a user using the secondary index.
            /// </summary>
            public async Task<ExampleUser?> GetUserByIndexAsync(string accountId, string userId)
            {
                return await _userRepository.GetByIndexAsync(accountId, userId, ExampleUser.AccountIdUserIdIndex);
            }

            /// <summary>
            /// Searches users by name or email.
            /// </summary>
            public async Task<List<ExampleUser>> SearchUsersAsync(string accountId, string searchText, int maxResults = 10)
            {
                var result = await _userRepository.SearchAsync(accountId, searchText, maxResults);
                return result.Entries;
            }

            /// <summary>
            /// Updates a user's name.
            /// </summary>
            public async Task<ExampleUser?> UpdateUserNameAsync(string accountId, string userId, string newName)
            {
                var user = await GetUserAsync(accountId, userId);
                if (user == null) return null;

                user.Name = newName;
                return await _userRepository.UpdateAsync(user, new List<string> { nameof(ExampleUser.Name) });
            }

            /// <summary>
            /// Deletes a user.
            /// </summary>
            public async Task<bool> DeleteUserAsync(string accountId, string userId)
            {
                return await _userRepository.DeleteAsync(accountId, userId);
            }

            /// <summary>
            /// Gets all users for an account.
            /// </summary>
            public async Task<List<ExampleUser>> GetAllUsersForAccountAsync(string accountId, int maxResults = 100)
            {
                var result = await _userRepository.QueryAsync(accountId, maxResults);
                return result.Entries;
            }


        }

        /// <summary>
        /// Example service that handles table management operations.
        /// Separated from data operations for better separation of concerns.
        /// </summary>
        public class ExampleTableManagementService
        {
            private readonly INoSQLTableManager<ExampleUser> _tableManager;

            public ExampleTableManagementService(INoSQLTableManager<ExampleUser> tableManager)
            {
                _tableManager = tableManager;
            }

            /// <summary>
            /// Creates the table with all indexes if it doesn't exist.
            /// </summary>
            public async Task<bool> EnsureTableExistsAsync()
            {
                return await _tableManager.CreateTableAsync();
            }

            /// <summary>
            /// Validates that the table configuration matches the model.
            /// </summary>
            public async Task<TableValidationResult> ValidateTableConfigurationAsync()
            {
                return await _tableManager.ValidateTableAsync();
            }

            /// <summary>
            /// Checks if table exists and is properly configured.
            /// </summary>
            public async Task<bool> IsTableReadyAsync()
            {
                if (!await _tableManager.TableExistsAsync())
                    return false;

                var validation = await _tableManager.ValidateTableAsync();
                return validation.IsValid;
            }

            /// <summary>
            /// Gets detailed schema information about the expected table structure.
            /// </summary>
            public async Task<TableSchemaInfo> GetExpectedSchemaAsync()
            {
                return await _tableManager.GetExpectedTableSchemaAsync();
            }

            /// <summary>
            /// Deletes the table (use with caution!).
            /// </summary>
            public async Task<bool> DeleteTableAsync()
            {
                return await _tableManager.DeleteTableAsync();
            }
        }

        /// <summary>
        /// Example of how to configure dependency injection.
        /// </summary>
        public static class ServiceConfiguration
        {
            /// <summary>
            /// Configures services in Program.cs or Startup.cs.
            /// </summary>
            public static void ConfigureServices(IServiceCollection services)
            {
                // Option 1: Add both repository and table manager with AWS services
                services.AddDynamoDBWithServices();

                // Option 2: Add only repository (for data operations only)
                services.AddDynamoDBWithRepository();

                // Option 3: Add only table manager (for table management only)
                services.AddDynamoDBWithTableManager();

                // Option 4: Add services separately (if AWS services already configured)
                // services.AddDynamoDBRepository();
                // services.AddDynamoDBTableManager();

                // Option 5: Add for specific model types
                // services.AddDynamoDBRepository<ExampleUser>();
                // services.AddDynamoDBTableManager<ExampleUser>();

                // Option 6: Add with custom configuration
                // services.AddDynamoDBWithServices(options =>
                // {
                //     options.AutoCreateTables = true;
                //     options.TableCreationTimeoutMinutes = 10;
                //     options.EnablePointInTimeRecovery = true;
                // });

                // Register your services
                services.AddScoped<ExampleUserService>();
                services.AddScoped<ExampleTableManagementService>();
            }
        }

        /// <summary>
        /// Example of how to use the services in a controller.
        /// Shows separation between data operations and table management.
        /// </summary>
        public class ExampleController
        {
            private readonly ExampleUserService _userService;
            private readonly ExampleTableManagementService _tableService;

            public ExampleController(ExampleUserService userService, ExampleTableManagementService tableService)
            {
                _userService = userService;
                _tableService = tableService;
            }

            // Data operations
            public async Task<ExampleUser?> CreateUser(string accountId, string name, string email)
            {
                var userId = Guid.NewGuid().ToString();
                return await _userService.CreateUserAsync(accountId, userId, name, email);
            }

            public async Task<ExampleUser?> GetUser(string accountId, string userId)
            {
                return await _userService.GetUserAsync(accountId, userId);
            }

            public async Task<List<ExampleUser>> SearchUsers(string accountId, string searchText)
            {
                return await _userService.SearchUsersAsync(accountId, searchText);
            }

            // Table management operations
            public async Task<bool> InitializeTable()
            {
                return await _tableService.EnsureTableExistsAsync();
            }

            public async Task<string> ValidateTableConfiguration()
            {
                var validation = await _tableService.ValidateTableConfigurationAsync();

                if (validation.IsValid)
                {
                    return "Table configuration is valid.";
                }

                return $"Table validation failed:\n{string.Join("\n", validation.ValidationErrors)}";
            }

            public async Task<TableSchemaInfo> GetTableSchema()
            {
                return await _tableService.GetExpectedSchemaAsync();
            }
        }

        /// <summary>
        /// Example of comprehensive table management operations.
        /// Shows how to use the separated table manager for infrastructure operations.
        /// </summary>
        public class TableManagementExample
        {
            private readonly INoSQLTableManager<ExampleUser> _tableManager;

            public TableManagementExample(INoSQLTableManager<ExampleUser> tableManager)
            {
                _tableManager = tableManager;
            }

            /// <summary>
            /// Complete table setup and validation example.
            /// </summary>
            public async Task<string> SetupAndValidateTableAsync()
            {
                var results = new List<string>();

                // Check if table exists
                bool tableExists = await _tableManager.TableExistsAsync();
                results.Add($"Table exists: {tableExists}");

                if (!tableExists)
                {
                    // Create table with all indexes
                    results.Add("Creating table...");
                    bool created = await _tableManager.CreateTableAsync();
                    results.Add($"Table creation result: {created}");
                }

                // Validate table configuration
                results.Add("Validating table configuration...");
                var validation = await _tableManager.ValidateTableAsync();

                results.Add($"Table validation result: {validation.IsValid}");
                results.Add($"Table status: {validation.TableStatus}");

                if (!validation.IsValid)
                {
                    results.Add("Validation errors:");
                    results.AddRange(validation.ValidationErrors);
                }

                // Show primary key validation
                results.Add($"Primary key - Expected Hash: {validation.PrimaryKey.ExpectedHashKey}, Actual: {validation.PrimaryKey.ActualHashKey}");
                results.Add($"Primary key - Expected Range: {validation.PrimaryKey.ExpectedRangeKey}, Actual: {validation.PrimaryKey.ActualRangeKey}");

                // Show secondary index validation
                foreach (var index in validation.SecondaryIndexes)
                {
                    results.Add($"Index '{index.IndexName}' ({index.IndexType}): Valid={index.IsValid}, Exists={index.IndexExists}");
                    if (!index.IsValid)
                    {
                        results.AddRange(index.ValidationErrors.Select(e => $"  - {e}"));
                    }
                }

                // Show expected schema information
                results.Add("\nExpected Table Schema:");
                var schema = await _tableManager.GetExpectedTableSchemaAsync();
                results.Add($"Table: {schema.TableName}");
                results.Add($"Hash Key: {schema.HashKeyAttribute}");
                results.Add($"Range Key: {schema.RangeKeyAttribute}");
                results.Add($"GSI Count: {schema.GlobalSecondaryIndexes.Count}");
                results.Add($"LSI Count: {schema.LocalSecondaryIndexes.Count}");

                return string.Join("\n", results);
            }
        }
    }
}
