﻿using Amazon;
//using Amazon.BedrockAgent;
//using Amazon.BedrockAgent.Model;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using platform.Constants;
using platform.Models.Request;
using platform.Models.Response;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Models.Response;
using System.Diagnostics.Eventing.Reader;


namespace platform.Controllers
{

    public partial class KnowledgeBaseController
    {



        #region CREATION PROCESS
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateDBEntry(AwsKnowledgeBase knowledgeBase)
        {
            return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()));
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessRollbackCreateDBEntry(AwsKnowledgeBase knowledgeBase)
        {
            return (await DeleteDBEntry(knowledgeBase)) ? (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()) : (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { ShouldUpdateDb = false });
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateKnowledgeBaseTask(AwsKnowledgeBase knowledgeBase)
        {
            var createKnowledgeBaseRequest = new Amazon.BedrockAgent.Model.CreateKnowledgeBaseRequest();
            createKnowledgeBaseRequest.ClientToken = knowledgeBase.KbId;
            createKnowledgeBaseRequest.Description = knowledgeBase.Description;
            createKnowledgeBaseRequest.Name = knowledgeBase.KbId;

            if (createKnowledgeBaseRequest.Description.Length == 0) createKnowledgeBaseRequest.Description = "no description";

            createKnowledgeBaseRequest.RoleArn = $"arn:aws:iam::{await iIAM.GetProviderAccountNumber()}:role/service-role/{kbConfiguration.CurrentValue.KnowledgeBaseRoleName}";

            createKnowledgeBaseRequest.KnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.KnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.Type = Amazon.BedrockAgent.KnowledgeBaseType.VECTOR;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.VectorKnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelArn = kbConfiguration.CurrentValue.DefaultEmbeddingsModelArn;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.EmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.BedrockEmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration.Dimensions = 1024;

            createKnowledgeBaseRequest.StorageConfiguration = new Amazon.BedrockAgent.Model.StorageConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.Type = Amazon.BedrockAgent.KnowledgeBaseStorageType.PINECONE;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration = new Amazon.BedrockAgent.Model.PineconeConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.Namespace = knowledgeBase.KbId;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.ConnectionString = kbConfiguration.CurrentValue.VectorStorePath;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.CredentialsSecretArn = kbConfiguration.CurrentValue.VectorStoreSecretArn;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping = new Amazon.BedrockAgent.Model.PineconeFieldMapping();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.MetadataField = kbConfiguration.CurrentValue.PineconeConfiguration.Metadata;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.TextField = kbConfiguration.CurrentValue.PineconeConfiguration.Text;

            try
            {
                var resp = await bedrockAgent.CreateKnowledgeBaseAsync(createKnowledgeBaseRequest);
                knowledgeBase.AwsData.AwsKbId = resp.KnowledgeBase.KnowledgeBaseId;

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult());
            }
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeleteKnowledgeBaseTask(AwsKnowledgeBase knowledgeBase)
        {
            try
            {
                var resp = await bedrockAgent.DeleteKnowledgeBaseAsync(new Amazon.BedrockAgent.Model.DeleteKnowledgeBaseRequest() { KnowledgeBaseId=knowledgeBase.AwsData.AwsKbId });
                knowledgeBase.AwsData.AwsKbId = null;

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult());
            }
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateDatasource(AwsKnowledgeBase knowledgeBase)
        {
            var kbRequest = new Amazon.BedrockAgent.Model.GetKnowledgeBaseRequest() { KnowledgeBaseId= knowledgeBase.AwsData.AwsKbId};
            var kbRequestResponse = await bedrockAgent.GetKnowledgeBaseAsync(kbRequest);

            if (kbRequestResponse == null || kbRequestResponse.KnowledgeBase.Status != Amazon.BedrockAgent.KnowledgeBaseStatus.ACTIVE)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 });
            }


            var awsRequest = new Amazon.BedrockAgent.Model.CreateDataSourceRequest();

            awsRequest.Name = knowledgeBase.KbId + "-ds";
            awsRequest.Description = knowledgeBase.Description;
            if (knowledgeBase.AwsData == null) throw new Exception("knjowledgebase provider data was null");
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;

            awsRequest.VectorIngestionConfiguration = new Amazon.BedrockAgent.Model.VectorIngestionConfiguration();

            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration = new Amazon.BedrockAgent.Model.ChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.ChunkingStrategy = Amazon.BedrockAgent.ChunkingStrategy.FIXED_SIZE;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration = new Amazon.BedrockAgent.Model.FixedSizeChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.MaxTokens = 300;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.OverlapPercentage = 20;

            awsRequest.DataSourceConfiguration = new Amazon.BedrockAgent.Model.DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.Type = Amazon.BedrockAgent.DataSourceType.S3;

            awsRequest.DataSourceConfiguration.S3Configuration = new Amazon.BedrockAgent.Model.S3DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.S3Configuration.BucketArn = kbConfiguration.CurrentValue.KnowledgeBaseBucketArn;
            awsRequest.DataSourceConfiguration.S3Configuration.InclusionPrefixes = new List<string> { $"kbs/AccountId={knowledgeBase.AccountId}/KbId={knowledgeBase.KbId}/" };
            awsRequest.ClientToken = knowledgeBase.KbId;

            var resp = await bedrockAgent.CreateDataSourceAsync(awsRequest);

            knowledgeBase.AwsData.ExternalDataSourcesIds.Add(resp.DataSource.DataSourceId);
            knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.DATASOURCE_CREATED;

            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessDeleteDatasource(AwsKnowledgeBase knowledgeBase)
        {
            var kbRequest = new Amazon.BedrockAgent.Model.GetKnowledgeBaseRequest() { KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId };
            var kbRequestResponse = await bedrockAgent.GetKnowledgeBaseAsync(kbRequest);

            if (kbRequestResponse == null || kbRequestResponse.KnowledgeBase.Status != Amazon.BedrockAgent.KnowledgeBaseStatus.ACTIVE)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 });
            }


            foreach(string ds in knowledgeBase.AwsData.ExternalDataSourcesIds)
            {
                var awsRequest = new Amazon.BedrockAgent.Model.DeleteDataSourceRequest();
                awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
                awsRequest.DataSourceId = ds;

                var resp = await bedrockAgent.DeleteDataSourceAsync(awsRequest);
            }
          
   
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private IMultiStepProcess<KnowledgeBaseStatus, AwsKnowledgeBase> GetCreationProcess(){

            var process = new MultiStepProcess<KnowledgeBaseStatus, AwsKnowledgeBase>()
                .ConfigureStep(KnowledgeBaseStatus.QUEUED, CreateProcessCreateDBEntry, CreateProcessRollbackCreateDBEntry, 10, 10, "Create DB Entry")
                .ConfigureStep(KnowledgeBaseStatus.DB_ENTRY_CREATED, CreateKnowledgeBaseTask, DeleteKnowledgeBaseTask, 10, 10, "Create KnowledgeBase")
                .ConfigureStep(KnowledgeBaseStatus.KB_CREATED, CreateProcessCreateDatasource, CreateProcessDeleteDatasource, 10, 10, "Create Datasource")
                .ConfigureStep(KnowledgeBaseStatus.DATASOURCE_CREATED, MultiStepProcessDummyTask, MultiStepProcessDummyTask, 1, 1, "Finish Process")
                .DefineSequence(
                    KnowledgeBaseStatus.QUEUED,
                    KnowledgeBaseStatus.DB_ENTRY_CREATED,
                    KnowledgeBaseStatus.KB_CREATED,
                    KnowledgeBaseStatus.DATASOURCE_CREATED,
                    KnowledgeBaseStatus.READY);

            return process;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateRequest([FromBody] Models.Request.KnowledgeBaseCreateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            string kbId = Guid.NewGuid().ToString();

            AwsKnowledgeBase knowledgeBase = new AwsKnowledgeBase();
            knowledgeBase.Name = request.Name;
            knowledgeBase.Description = request.Description;
            knowledgeBase.AccountId = accountId;
            knowledgeBase.KbId = kbId;
            knowledgeBase.Status = KnowledgeBaseStatus.QUEUED;
            knowledgeBase.LastChangeTimestamp = CurrentTimestamp();
            knowledgeBase.AwsData = new AwsKnowledgeBaseData();

            var process = GetCreationProcess();
            process.StateData.StatefulObject = knowledgeBase;

            var messageBusParams = new MessageBusDispachParams() { 
                Controller = Routes.KnowledgeBaseController.BasePath, 
                Route = Routes.KnowledgeBaseController.Internal.CREATION_PROCESS, 
                TargetMicroservice = MicroserviceType.Platform, 
                Payload = process
            };

            await DispatchApiEventV2(messageBusParams);

            return Ok(request);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> CreateProcess(MessageBusMessage message)
        {
            return await MultiStepProcess(message, GetCreationProcess());
        }
        #endregion

    


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet]
        public async Task<IActionResult> ListKnowledgeBases(int count = 0, string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;

            
            var listEntriesInternal = await GetDBEntries<AwsKnowledgeBase>(nameof(AwsKnowledgeBase.AccountId), GetAccountId(), count, nextToken, getTotal: nextToken == null);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<KnowledgeBaseResponse>()
            {
                Entries = mapper.Map<IList<AwsKnowledgeBase>, IList<KnowledgeBaseResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPut(Constants.Routes.KnowledgeBaseController.Public.PUT)]
        public async Task<IActionResult> PutKnowledgeBase(string kbId, [FromBody] AgentPutRequest knowledgeBasePutRequest) {
            string accountId = GetAccountId();
            KnowledgeBase kb = await GetDBEntry<KnowledgeBase>(accountId, kbId);
            if (kb == null) return BadRequest("Not found");

            kb.Name = knowledgeBasePutRequest.Name;
            kb.Description = knowledgeBasePutRequest.Description;

            if (await UpdateDBEntry<KnowledgeBase>(kb, new List<string>() { nameof(KnowledgeBase.Name), nameof(KnowledgeBase.Description) }) == null)
            {
                return StatusCode(500, "Couldn't update");
            }
            return Ok(kb);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_KNOWLEDGEBASE)]
        public async Task<IActionResult> GetKnowledgeBase(string kbId)
        {
            string accountId = GetAccountId();
            AwsKnowledgeBase kb = await GetDBEntry<AwsKnowledgeBase>(accountId, kbId);
            if (kb == null) return BadRequest("Not found");

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();
            
            return Ok(mapper.Map<AwsKnowledgeBase, KnowledgeBaseResponse>(kb));
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_AGENTS_FOR_KNOWLEDGE_BASE)]
        public async Task<IActionResult> GetAgentsForKnowledgeBase(string kbId, [FromQuery] string? nextToken = null, [FromQuery] int count = -1)
        {
            string accountId = GetAccountId();
            ListResponse<AgentKnowledgeBase> response = await GetDBEntries<AgentKnowledgeBase>(
                nameof(AgentKnowledgeBase.KnowledgebaseId),
                kbId,
                count,
                nextToken,
                AgentKnowledgeBase.KnowledgebaseIdHashIndex,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(AgentKnowledgeBase.AccountId)}", nameof(AgentKnowledgeBase.AccountId) } },
                    ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry> { { $":{nameof(AgentKnowledgeBase.AccountId)}", accountId } },
                    ExpressionStatement = $"#{nameof(AgentKnowledgeBase.AccountId)}=:{nameof(AgentKnowledgeBase.AccountId)}"
                },
                getTotal: false
            ); 

            if (response == null) return BadRequest("Not found");
            if (response.Entries.Count == 0)
            {
                return Ok(new ListResponse<AgentResponse>());
            }

            var attValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>();
            var statement = new List<string>();

            foreach (var item in response.Entries.Select((value, i) => new { i, value }))
            {
                if (item.value.AccountId != accountId) continue;
                attValues.Add($":kb{item.i}", item.value.AgentId);
                statement.Add($"#{nameof(Agent.AgentId)}=:kb{item.i}");
            }

            ListResponse<Agent> agents = await GetDBEntries<Agent>(
                nameof(Agent.AccountId),
                GetAccountId(),
                response.Entries.Count,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(Agent.AgentId)}", nameof(Agent.AgentId) } },
                    ExpressionAttributeValues = attValues,
                    ExpressionStatement = String.Join(" OR ", statement)
                },
                getTotal: false
            );

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Agent, AgentResponse>();
            }, loggerFactory).CreateMapper();

            var finalResponse = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<Agent>, IList<AgentResponse>>(agents.Entries).ToList(),
                NextToken = agents.NextToken,
                Total = agents.Total,
            };

            return Ok(finalResponse);
        }



    }
}
        