﻿using shared.Components.BehaviorTreeBase.Condition.InputCondition;
using shared.Components.BehaviorTreeBase.Models;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTree.Condition
{
    [JsonDerivedType(typeof(StringInputCondition), typeDiscriminator: nameof(StringInputCondition))]
    public interface ICondition
    {
        public bool Evaluate(TreeState treeState);
    }
}
