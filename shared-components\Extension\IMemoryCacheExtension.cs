﻿using Microsoft.Extensions.Caching.Memory;

namespace shared.Extension
{
    public static class IMemoryCacheExtension
    {

        public static async Task<TResult?> GetOrCreateAsync<TResult>(this IMemoryCache memcache, Func<ICacheEntry, Task<TResult>> method)
        {
            return await memcache.GetOrCreateAsync<TResult>(
                (memcache.GetType().FullName ?? memcache.GetType().Name) + ":" + method.Method.Name,
                method
            ) ?? default(TResult);
        }

        public static async Task<TResult?> GetOrCreateAsync<TResult>(this IMemoryCache memcache, Func<Task<TResult>> method)
        {
            return await memcache.GetOrCreateAsync<TResult>(
                (memcache.GetType().FullName ?? memcache.GetType().Name) + ":" + method.Method.Name,
                (cacheEntry) => { return method(); }
            ) ?? default(TResult);
        }
    }
}
