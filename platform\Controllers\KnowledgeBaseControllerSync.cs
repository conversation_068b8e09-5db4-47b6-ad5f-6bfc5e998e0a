﻿using Amazon.BedrockAgent;
using Amazon.DynamoDBv2.DocumentModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Converters;

namespace platform.Controllers
{

    public partial class KnowledgeBaseController
    {

        private async Task<List<KnowledgeBaseFile>> GetPendingUploadsToKnowledgeBase(KnowledgeBase knowledgeBase, bool getAll = false)
        {
            var enumConvert = new DynamoEnumStringConverter<KnowledgeBaseFileUploadStatus>();
            var queryConf = new QueryOperationConfig();

            queryConf.Limit = getAll ? 100 : 1;
            queryConf.AttributesToGet = new List<string>() { nameof(KnowledgeBaseFile.FileId) };
            queryConf.FilterExpression = new Expression();
            queryConf.FilterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", "Status" } };
            queryConf.FilterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":s", enumConvert.ToEntry(KnowledgeBaseFileUploadStatus.READY) } };
            queryConf.FilterExpression.ExpressionStatement = "#S <> :s";
            queryConf.KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            queryConf.KeyExpression.ExpressionStatement = "#K = :k";
            queryConf.KeyExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#K", nameof(KnowledgeBaseFile.KbId) } };
            queryConf.KeyExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":k", new Primitive(knowledgeBase.KbId) } };
            queryConf.Select = SelectValues.SpecificAttributes;

            var query = dBContext.FromQueryAsync<KnowledgeBaseFile>(queryConf);
            var pendingFiles = getAll ? await query.GetNextSetAsync() : await query.GetRemainingAsync();

            return pendingFiles ?? new List<KnowledgeBaseFile>();
        }

        private async Task<bool> TagFilesAsDeployed(KnowledgeBase knowledgeBase)
        {
            var enumConvert = new DynamoEnumStringConverter<KnowledgeBaseFileUploadStatus>();
            var queryConf = new QueryOperationConfig();

            queryConf.Limit = 10;
            queryConf.FilterExpression = new Expression();
            queryConf.FilterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", "Status" }, { "#D", "IsDeployed" } };
            queryConf.FilterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":s", enumConvert.ToEntry(KnowledgeBaseFileUploadStatus.READY) }, { ":d", false } };
            queryConf.FilterExpression.ExpressionStatement = "#S = :s and #D = :d";
            queryConf.KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            queryConf.KeyExpression.ExpressionStatement = "#K = :k";
            queryConf.KeyExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#K", nameof(KnowledgeBaseFile.KbId) } };
            queryConf.KeyExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":k", knowledgeBase.KbId } };

            var query = dBContext.FromQueryAsync<KnowledgeBaseFile>(queryConf);
            List<KnowledgeBaseFile>? pendingFiles = await query.GetNextSetAsync();
            while (pendingFiles != null && pendingFiles.Count > 0){
                var bw = dBContext.CreateBatchWrite<KnowledgeBaseFile>();
                pendingFiles.ForEach(x => x.IsDeployed =  true);
                bw.AddPutItems(pendingFiles);
                try
                {
                    await bw.ExecuteAsync();
                    pendingFiles = await query.GetNextSetAsync();
                }
                catch (Exception ex) {
                    return false;
                }
            }
            return true;
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.KnowledgeBaseController.Public.DEPLOY)]
        public async Task<IActionResult> DeployRequest(string kbId, [FromBody] Models.Request.KnowledgeBaseDeployRequest request)
        {

            string accountId = GetAccountId();

            AwsKnowledgeBase? knowledgeBase = await GetDBEntry<AwsKnowledgeBase>(accountId, kbId);

            if (knowledgeBase.Tag == request.Tag) return BadRequest("Trying to set the same tag for a knowledgebase");

            if (knowledgeBase.Status != shared.Models.Enums.KnowledgeBaseStatus.READY)
            {
                return BadRequest("Knowledge must be in ready state to deploy.");
            }

            if ((await GetPendingUploadsToKnowledgeBase(knowledgeBase)).Count > 0)
            {
                return BadRequest("Can deploy while adding files to the knowledgebase.");
            }

            knowledgeBase = await SetDBEntryStatusAtomic(knowledgeBase, shared.Models.Enums.KnowledgeBaseStatus.SEALING, shared.Models.Enums.KnowledgeBaseStatus.READY);

            if (knowledgeBase == null) {
                return BadRequest("KnowledgeBase not in READY state to deploy, please make sure no other operations are running before deploying it.");
            }

            knowledgeBase.Tag = request.Tag;

            await DispatchApiEvent(
                knowledgeBase,
                MicroserviceType.Platform,
                Constants.Routes.KnowledgeBaseController.BasePath,
                Constants.Routes.KnowledgeBaseController.Internal.DEPLOY_PROCESS, 3);

            return Ok(request);
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.KnowledgeBaseController.Internal.DEPLOY_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> DeployKbProcess([FromBody] AwsKnowledgeBase knowledgeBase)
        {
            string accountId = GetAccountId();
            int requeueDelay = 5;
            //ToDo: check warnings and failed files in sync
            switch (knowledgeBase.Status)
            {
                case shared.Models.Enums.KnowledgeBaseStatus.SEALING:
                    {
                        var pendingFiles = await GetPendingUploadsToKnowledgeBase(knowledgeBase);

                        if (pendingFiles.Count == 0)
                        {
                            knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.DEPLOYING;
                            requeueDelay = 0;
                            await PutDBEntry(knowledgeBase);
                        }
                        else
                        {
                            requeueDelay = 10;
                        }
                        break;
                    }
                case shared.Models.Enums.KnowledgeBaseStatus.DEPLOYING:
                    {
                        var bedrockRequest = new Amazon.BedrockAgent.Model.StartIngestionJobRequest();
                        bedrockRequest.ClientToken = knowledgeBase.KbId;
                        bedrockRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
                        bedrockRequest.DataSourceId = knowledgeBase.AwsData.ExternalDataSourcesIds[0];
                        var injestionResponse = await bedrockAgent.StartIngestionJobAsync(bedrockRequest);
                        knowledgeBase.AwsData.ExternalIngestionJobId = injestionResponse.IngestionJob.IngestionJobId;

                        knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.SYNCRONIZING;
                        await PutDBEntry(knowledgeBase);
                    }
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.SYNCRONIZING:
                    {
                        var request = new Amazon.BedrockAgent.Model.GetIngestionJobRequest();
                        AwsKnowledgeBaseData awsKnowledgeBaseData = knowledgeBase.AwsData;
                        request.KnowledgeBaseId = awsKnowledgeBaseData.AwsKbId;
                        request.IngestionJobId = awsKnowledgeBaseData.ExternalIngestionJobId;
                        request.DataSourceId = awsKnowledgeBaseData.ExternalDataSourcesIds[0];

                        var jobStatusResponse = await bedrockAgent.GetIngestionJobAsync(request);

                        switch (jobStatusResponse.IngestionJob.Status.Value)
                        {
                            case var value when value == IngestionJobStatus.STARTING.Value:
                                requeueDelay = 5;
                                break;
                            case var value when value == IngestionJobStatus.IN_PROGRESS.Value:
                                requeueDelay = 30;
                                break;
                            case var value when value == IngestionJobStatus.FAILED.Value:
                                knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.READY;
                                if (await PutDBEntry(knowledgeBase) == null)
                                {
                                    requeueDelay = 10;
                                }
                                else
                                {
                                    logger.LogError($"AWS API Failed to sync data for AccountId={accountId} and KbId={knowledgeBase.KbId}, external params = {knowledgeBase.AwsData}");
                                    return Ok();
                                }
                                break;
                            case var value when value == IngestionJobStatus.COMPLETE.Value:

                                if (jobStatusResponse.IngestionJob.Statistics.NumberOfDocumentsFailed > 0)
                                {
                                    knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.READY;
                                    knowledgeBase.Tag = string.Empty;
                                    if (await PutDBEntry(knowledgeBase) == null)
                                    {
                                        requeueDelay = 10;
                                    }
                                    else
                                    {
                                        logger.LogError($"AWS API Failed to sync data for AccountId={accountId} and KbId={knowledgeBase.KbId}, external params = {knowledgeBase.AwsData}");
                                        return Ok();
                                    }
                                    break;
                                }
                                else
                                {
                                    knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.TAGGING;
                                    await PutDBEntry(knowledgeBase);
                                    requeueDelay = 0;
                                }
                                break;
                        }
                    }
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.TAGGING:
                    if (await TagFilesAsDeployed(knowledgeBase))
                    {
                        knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.READY;
                        if (await PutDBEntry(knowledgeBase) == null)
                        {
                            requeueDelay = 10;
                        }
                        else
                        {
                            return Ok();
                        }
                    }
                    else
                    {
                        requeueDelay = 5;
                    }
                    break;
            }           

            await DispatchApiEvent(
                knowledgeBase, 
                MicroserviceType.Platform, 
                Constants.Routes.KnowledgeBaseController.BasePath, 
                Constants.Routes.KnowledgeBaseController.Internal.DEPLOY_PROCESS, 
                requeueDelay);

            return Ok();
        }
    }
}
