namespace shared.Components.MessageBus.Configuration
{
    /// <summary>
    /// Configuration options for the message bus
    /// </summary>
    public class MessageBusConfiguration
    {
        /// <summary>
        /// Enable message deduplication using IMemoryCache
        /// </summary>
        public bool EnableDeduplication { get; set; } = false;

        /// <summary>
        /// Maximum deduplication period in minutes (how long to keep message IDs in cache)
        /// </summary>
        public int MaximumDeduplicationPeriodMinutes { get; set; } = 10;

        /// <summary>
        /// Visibility timeout in seconds (how long a message is invisible after being received)
        /// </summary>
        public int VisibilityTimeoutSeconds { get; set; } = 120;

        /// <summary>
        /// Maximum number of messages to process concurrently
        /// </summary>
        public int MaxConcurrentMessages { get; set; } = 10;

        /// <summary>
        /// Long polling wait time in seconds
        /// </summary>
        public int LongPollingWaitTimeSeconds { get; set; } = 20;

        /// <summary>
        /// Default retry policy settings
        /// </summary>
        public RetryPolicyConfiguration DefaultRetryPolicy { get; set; } = new RetryPolicyConfiguration();

        /// <summary>
        /// Queue creation settings
        /// </summary>
        public QueueConfiguration QueueSettings { get; set; } = new QueueConfiguration();
    }

    /// <summary>
    /// Retry policy configuration
    /// </summary>
    public class RetryPolicyConfiguration
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Base delay in seconds for exponential backoff
        /// </summary>
        public int BaseDelaySeconds { get; set; } = 1;

        /// <summary>
        /// Maximum delay in seconds
        /// </summary>
        public int MaxDelaySeconds { get; set; } = 300;

        /// <summary>
        /// Multiplier for exponential backoff
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// Whether to add jitter to prevent thundering herd
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// Jitter factor (0.0 to 1.0)
        /// </summary>
        public double JitterFactor { get; set; } = 0.1;
    }

    /// <summary>
    /// Queue configuration settings
    /// </summary>
    public class QueueConfiguration
    {
        /// <summary>
        /// Default visibility timeout for created queues
        /// </summary>
        public int DefaultVisibilityTimeoutSeconds { get; set; } = 120;

        /// <summary>
        /// Default message retention period in seconds
        /// </summary>
        public int MessageRetentionPeriodSeconds { get; set; } = 1209600; // 14 days

        /// <summary>
        /// Maximum message size in bytes
        /// </summary>
        public int MaxMessageSizeBytes { get; set; } = 262144; // 256 KB

        /// <summary>
        /// Delay seconds for created queues
        /// </summary>
        public int DelaySeconds { get; set; } = 0;

        /// <summary>
        /// Receive message wait time for created queues
        /// </summary>
        public int ReceiveMessageWaitTimeSeconds { get; set; } = 20;

        /// <summary>
        /// Enable dead letter queue
        /// </summary>
        public bool EnableDeadLetterQueue { get; set; } = true;

        /// <summary>
        /// Maximum receives before moving to dead letter queue
        /// </summary>
        public int MaxReceiveCount { get; set; } = 3;
    }
}
