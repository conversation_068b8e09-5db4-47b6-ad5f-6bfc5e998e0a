﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using System;
using System.Text.Json;

namespace shared.Converters
{
    public class DynamoDBJsonFallback<T> : IPropertyConverter
    {
        public object? FromEntry(DynamoDBEntry entry)
        {
            return JsonSerializer.Deserialize<T>(entry.AsString());
        }

        public DynamoDBEntry ToEntry(object value)
        {
            var jsonValue = JsonSerializer.Serialize<T>((T)value);
            var p = new Primitive(jsonValue);
            return p;
        }
    }
}
