﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Models
{
    public class BehaviorTreeEvaluationResult
    {
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<NodeState>))]
        [JsonConverter(typeof(JsonEnumStringConverter<NodeState>))]
        public NodeState TreeNodesState { get; set; } = NodeState.UNKNOWN;

        public bool IsFinished()
        {
            return TreeNodesState != NodeState.UNKNOWN && TreeNodesState != NodeState.RUNNING;
        }
    }
}
