﻿using Amazon.S3.Model;
using Amazon.S3;

namespace platform.Components.AWS
{
    public class S3Helper
    {
        public static async Task<bool> UploadFullFile(IAmazonS3 client, IFormFile file, string bucketName, string key)
        {
            var bucketExists = await Amazon.S3.Util.AmazonS3Util.DoesS3BucketExistV2Async(client, bucketName);

            if (!bucketExists) return false;
            var request = new PutObjectRequest()
            {
                BucketName = bucketName,
                Key = key,
                InputStream = file.OpenReadStream()
            };

            request.Metadata.Add("Content-Type", file.ContentType);
            await client.PutObjectAsync(request);

            return true;
        }

        public static async Task<bool> DeleteFile(IAmazonS3 client, string bucketName, string key) {
            var bucketExists = await Amazon.S3.Util.AmazonS3Util.DoesS3BucketExistV2Async(client, bucketName);

            if (!bucketExists) return false;

            var request = new DeleteObjectRequest() { BucketName = bucketName, Key = key };

            await client.DeleteObjectAsync(request);

            return true;
        }

        public static async Task<bool> BucketExists(IAmazonS3 client, string bucketName)
        {
            return await Amazon.S3.Util.AmazonS3Util.DoesS3BucketExistV2Async(client, bucketName);
        }
    }
}
