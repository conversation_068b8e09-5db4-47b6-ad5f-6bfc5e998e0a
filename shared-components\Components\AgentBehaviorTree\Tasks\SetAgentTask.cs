﻿using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;
using shared.Types;

namespace shared.Components.AgentBehaviorTree.Tasks
{
    public class SetAgentTask : Node
    {
        public PriorityValue<AgentReference> AgentReference { get; set; }

        public SetAgentTask(string nodeId, List<Node>? children, ConditionExpression? conditionExpression, PriorityValue<AgentReference> agentReference) : base(nodeId, children, conditionExpression)
        {
            AgentReference = agentReference;
        }

        protected override Task<NodeState> EvaluateImpl(TreeState treeState)
        {
            ((AgentTreeState)treeState).Result.AgentReference.Set(AgentReference);
            return Task.FromResult(NodeState.SUCCESS);
        }
    }
}
