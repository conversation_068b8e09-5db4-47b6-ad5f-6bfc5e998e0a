using System.Reflection;

namespace shared.Models.Documents
{
    /// <summary>
    /// Base class for NoSQL models that provides static key resolution methods.
    /// Uses the "new" keyword to allow static polymorphism across inheritance hierarchy.
    /// </summary>
    public abstract class NoSQLModel
    {
        public int DataVersion { get; set; } = 0;
        public int ModelVersion { get; set; } = 0;
        public long LastChangeTimestamp { get; set; } = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        public long CreatedAt { get; set; } = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        public string SearchString { get; set; } = string.Empty;

        /// <summary>
        /// Gets the hash key property name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            throw new NotImplementedException("GetHashKeyPropertyName must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the range key property name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            throw new NotImplementedException("GetRangeKeyPropertyName must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the hash key value from the model instance.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key value</returns>
        public virtual string? GetHashKeyValue(string? indexName = null)
        {
            throw new NotImplementedException("GetHashKeyValue must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the range key value from the model instance.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key value</returns>
        public virtual string? GetRangeKeyValue(string? indexName = null)
        {
            throw new NotImplementedException("GetRangeKeyValue must be implemented in derived classes");
        }

        /// <summary>
        /// Gets the table name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            throw new NotImplementedException("GetTableName must be implemented in derived classes");
        }

        /// <summary>
        /// Generates search string for the model.
        /// Override in derived classes to provide specific search logic.
        /// </summary>
        /// <returns>Search string</returns>
        public virtual string GetSearchString() 
        { 
            return string.Empty; 
        }

        /// <summary>
        /// Gets property value by name using reflection.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Property value</returns>
        protected object? GetPropertyValue(string propertyName)
        {
            PropertyInfo? property = GetType().GetProperty(propertyName);
            return property?.GetValue(this);
        }

        /// <summary>
        /// Sets property value by name using reflection.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <param name="value">Value to set</param>
        protected void SetPropertyValue(string propertyName, object? value)
        {
            PropertyInfo? property = GetType().GetProperty(propertyName);
            property?.SetValue(this, value);
        }

        /// <summary>
        /// Gets property by attribute type using reflection.
        /// </summary>
        /// <param name="type">Type to search</param>
        /// <param name="attributeType">Attribute type to find</param>
        /// <returns>Property info</returns>
        public static PropertyInfo? GetPropertyByAttribute(Type type, Type attributeType)
        {
            return type.GetProperties()
                .Select(pi => new { Property = pi, Attribute = pi.GetCustomAttributes(attributeType, true).FirstOrDefault() })
                .Where(x => x.Attribute != null)
                .FirstOrDefault()?.Property;
        }
    }
}
