﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTree.Node;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Models.Document
{
    public class JsonBehaviorTree 
    { 
        public string TreeId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Node>))]
        public Node? TreeTemplate { get; set; } = null;
        public Dictionary<string, BehaviorTreeInput> BehaviorTreeInputs { get; set; } = new Dictionary<string, BehaviorTreeInput>();
    }
}
