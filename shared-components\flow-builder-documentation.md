# Flow Builder Documentation

## Overview

The Flow Builder is a visual workflow editor built with React Flow that allows users to create, edit, and manage complex behavioral flows for AI agents. It provides a drag-and-drop interface for building node-based workflows with support for various control structures, task execution, and conditional logic.

## Core Components

### 1. Nodes
Nodes are the fundamental building blocks of a flow, representing different types of operations or control structures.

#### BaseNode Structure
```typescript
interface BaseNode<T> {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    baseData: BaseNodeData<T>;
    events?: NodeEvents<T>;
  };
}

interface BaseNodeData<T> {
  name: string;
  nodeData: T;
  nodeConditions?: RuleGroupType;
}
```

#### Available Node Types

1. **Set Agent Node** - Configures which agent to use for execution
   - **Inputs**: 1 (target handle on left)
   - **Outputs**: 1 (source handle on right)
   - **Configuration**: Agent ID, alias/tag selection
   - **Purpose**: Sets the active agent for subsequent operations

2. **Task Node** - Executes specific tasks with configurable parameters
   - **Inputs**: 1 (trigger handle on left)
   - **Outputs**: 1 (next handle on right)
   - **Configuration**: Multiple tasks can be added with different types
   - **Purpose**: Performs actual work operations

3. **Sequence Node** - Executes child nodes in sequential order
   - **Inputs**: 1 (input handle on left)
   - **Outputs**: Configurable (default 2, can be increased)
   - **Configuration**: Number of output connections
   - **Purpose**: Ensures ordered execution of connected nodes

4. **Parallel Node** - Executes child nodes simultaneously
   - **Inputs**: 1 (input handle on left)
   - **Outputs**: Configurable (default 2, can be increased)
   - **Configuration**: Number of parallel execution paths
   - **Purpose**: Enables concurrent execution of multiple branches

5. **Select Node** - Chooses between multiple execution paths
   - **Inputs**: 1 (input handle on left)
   - **Outputs**: Configurable (default 2, can be increased)
   - **Configuration**: Number of selection options
   - **Purpose**: Selects first successful execution path

6. **Loop Node** - Repeats execution based on conditions
   - **Inputs**: 1 (input handle on left)
   - **Outputs**: 2 (loop handle at 33%, exit handle at 66%)
   - **Configuration**: Loop condition using query builder
   - **Purpose**: Repeats execution while condition is true

7. **Switch Node** - Conditional branching with multiple conditions
   - **Inputs**: 1 (input handle on left)
   - **Outputs**: N+1 (N condition outputs + 1 default output)
   - **Configuration**: Multiple conditional branches with query builder
   - **Purpose**: Routes execution based on conditional logic

#### Available Task Types

Tasks are the executable units within Task Nodes. Each task type has specific configuration options and purposes:

1. **Dummy Task**
   - **Purpose**: Simple demonstration task for testing and prototyping
   - **Configuration**:
     - `message`: String field for custom message content
   - **Use Case**: Testing flows, placeholder operations, debugging

2. **Conditional Task**
   - **Purpose**: Complex conditional logic with multiple IF/ELSE IF/ELSE branches
   - **Configuration**:
     - `clauses`: Array of conditional clauses with conditions and associated tasks
     - `elseTasks`: Tasks to execute if no conditions match
   - **Features**:
     - Multiple IF/ELSE IF conditions using query builder
     - Each clause can contain multiple sub-tasks
     - Supports nested task execution within conditions
   - **Use Case**: Complex decision-making, multi-branch logic, conditional workflows

#### Task Configuration Structure

```typescript
interface BaseTaskProps<T> {
  name: string;
  taskData: T;
}

// Example task configurations:
interface DummyTaskData {
  message: string;
}

interface ConditionalTaskData {
  clauses: ConditionalClause[];
  elseTasks: BaseTaskProps<any>[];
}

interface ConditionalClause {
  condition: RuleGroupType;
  tasks: BaseTaskProps<any>[];
}
```

### 2. Edges
Edges define the connections and flow between nodes, determining execution order and data flow.

#### Edge Structure
```typescript
interface Edge {
  id: string;
  source: string;      // Source node ID
  target: string;      // Target node ID
  sourceHandle?: string;
  targetHandle?: string;
  animated?: boolean;
}
```

### 3. Fields
Fields represent variables and inputs that can be used throughout the flow.

#### FieldModel Structure
```typescript
interface FieldModel {
  id: string;
  type: 'variable' | 'input';
  name: string;
  dataType: 'STRING' | 'INTEGER' | 'FLOAT' | 'BOOLEAN' | 'JSON';
  required: boolean;
  defaultValue: string;
}
```

## Data Structure Examples

### Example Flow with Nodes and Tasks

```json
{
  "nodes": [
    {
      "id": "node-1",
      "type": "SetAgent",
      "position": { "x": 100, "y": 100 },
      "data": {
        "baseData": {
          "name": "Initialize Agent",
          "nodeData": {}
        },
        "events": {
          "onOpenDrawer": "function"
        }
      }
    },
    {
      "id": "node-2",
      "type": "Task",
      "position": { "x": 300, "y": 100 },
      "data": {
        "baseData": {
          "name": "Process Data",
          "nodeData": {
            "tasks": [
              {
                "id": "task-1",
                "type": "DataProcessing",
                "config": {
                  "message": "Process user input"
                }
              }
            ]
          }
        }
      }
    },
    {
      "id": "node-3",
      "type": "Switch",
      "position": { "x": 500, "y": 100 },
      "data": {
        "baseData": {
          "name": "Decision Point",
          "nodeData": {
            "conditions": [
              {
                "id": "condition-1",
                "condition": {
                  "combinator": "and",
                  "rules": [
                    {
                      "field": "input.status",
                      "operator": "=",
                      "value": "success"
                    }
                  ]
                },
                "label": "Success Path"
              }
            ]
          }
        }
      }
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "node-1",
      "target": "node-2",
      "animated": true
    },
    {
      "id": "edge-2",
      "source": "node-2",
      "target": "node-3",
      "animated": true
    }
  ],
  "fields": [
    {
      "id": "field-1",
      "type": "input",
      "name": "User Input",
      "dataType": "STRING",
      "required": true,
      "defaultValue": ""
    },
    {
      "id": "field-2",
      "type": "variable",
      "name": "Processing Status",
      "dataType": "STRING",
      "required": false,
      "defaultValue": "pending"
    }
  ]
}
```

### Example Node Configurations

#### Loop Node with Condition
```json
{
  "id": "loop-node",
  "type": "Loop",
  "position": { "x": 200, "y": 200 },
  "data": {
    "baseData": {
      "name": "Retry Loop",
      "nodeData": {
        "condition": {
          "combinator": "and",
          "rules": [
            {
              "field": "variable.retryCount",
              "operator": "<",
              "value": "3"
            }
          ]
        }
      }
    }
  }
}
```

#### Task Node with Multiple Tasks
```json
{
  "id": "task-node",
  "type": "Task",
  "position": { "x": 300, "y": 100 },
  "data": {
    "baseData": {
      "name": "Complex Processing",
      "nodeData": {
        "tasks": [
          {
            "name": "Dummy Task",
            "taskData": {
              "message": "Initialize processing"
            }
          },
          {
            "name": "Conditional Task",
            "taskData": {
              "clauses": [
                {
                  "condition": {
                    "combinator": "and",
                    "rules": [
                      {
                        "field": "input.type",
                        "operator": "=",
                        "value": "urgent"
                      }
                    ]
                  },
                  "tasks": [
                    {
                      "name": "Dummy Task",
                      "taskData": {
                        "message": "Handle urgent request"
                      }
                    }
                  ]
                }
              ],
              "elseTasks": [
                {
                  "name": "Dummy Task",
                  "taskData": {
                    "message": "Handle normal request"
                  }
                }
              ]
            }
          }
        ]
      }
    }
  }
}
```

#### Switch Node with Multiple Conditions
```json
{
  "id": "switch-node",
  "type": "Switch",
  "position": { "x": 500, "y": 100 },
  "data": {
    "baseData": {
      "name": "Status Router",
      "nodeData": {
        "conditions": [
          {
            "id": "condition-1",
            "condition": {
              "combinator": "and",
              "rules": [
                {
                  "field": "input.status",
                  "operator": "=",
                  "value": "success"
                }
              ]
            },
            "label": "Success Path"
          },
          {
            "id": "condition-2",
            "condition": {
              "combinator": "and",
              "rules": [
                {
                  "field": "input.status",
                  "operator": "=",
                  "value": "error"
                }
              ]
            },
            "label": "Error Path"
          }
        ]
      }
    }
  }
}
```

#### Set Agent Node Configuration
```json
{
  "id": "set-agent-node",
  "type": "SetAgent",
  "position": { "x": 100, "y": 100 },
  "data": {
    "baseData": {
      "name": "Initialize Agent",
      "nodeData": {
        "agentId": "agent-123",
        "isAlias": true,
        "agentAliasOrTag": "production-agent"
      }
    }
  }
}
```

## Handle Configurations and Connections

### Node Handle Details

Each node type has specific handle configurations that determine how they can be connected:

#### Single Input/Output Nodes
- **Set Agent Node**: `target: "input"` → `source: "output"`
- **Task Node**: `target: "trigger"` → `source: "next"`

#### Multi-Output Nodes
- **Sequence Node**: `target: "input"` → `source: "output-0", "output-1", ...`
- **Parallel Node**: `target: "input"` → `source: "output-0", "output-1", ...`
- **Select Node**: `target: "input"` → `source: "output-0", "output-1", ...`

#### Conditional Nodes
- **Loop Node**: `target: "input"` → `source: "loop"` (continue), `source: "exit"` (break)
- **Switch Node**: `target: "input"` → `source: "condition-0", "condition-1", ..., "default"`

### Edge Connection Examples

```json
{
  "edges": [
    {
      "id": "edge-1",
      "source": "set-agent-node",
      "target": "task-node",
      "sourceHandle": "output",
      "targetHandle": "trigger",
      "animated": true
    },
    {
      "id": "edge-2",
      "source": "sequence-node",
      "target": "parallel-node-1",
      "sourceHandle": "output-0",
      "targetHandle": "input",
      "animated": true
    },
    {
      "id": "edge-3",
      "source": "sequence-node",
      "target": "parallel-node-2",
      "sourceHandle": "output-1",
      "targetHandle": "input",
      "animated": true
    },
    {
      "id": "edge-4",
      "source": "loop-node",
      "target": "task-node",
      "sourceHandle": "loop",
      "targetHandle": "trigger",
      "animated": true
    },
    {
      "id": "edge-5",
      "source": "switch-node",
      "target": "success-handler",
      "sourceHandle": "condition-0",
      "targetHandle": "input",
      "animated": true
    },
    {
      "id": "edge-6",
      "source": "switch-node",
      "target": "error-handler",
      "sourceHandle": "condition-1",
      "targetHandle": "input",
      "animated": true
    },
    {
      "id": "edge-7",
      "source": "switch-node",
      "target": "default-handler",
      "sourceHandle": "default",
      "targetHandle": "input",
      "animated": true
    }
  ]
}
```

## Key Features

### Visual Editor
- Drag-and-drop interface for adding nodes
- Visual connections between nodes
- Real-time flow visualization
- Minimap and controls for navigation

### Node Configuration
- Each node type has specific configuration options
- Conditional logic support using query builder
- Task parameter configuration
- Dynamic field referencing

### Data Flow Management
- Input/output variable management
- Field type validation
- Default value assignment
- Required field enforcement

### Persistence
The Flow Builder saves the complete flow definition as JSON, including:
- All node configurations and positions
- Edge connections and routing
- Field definitions and metadata
- Conditional logic and rules

This data structure enables complete flow reconstruction, execution planning, and version control of behavioral workflows.
