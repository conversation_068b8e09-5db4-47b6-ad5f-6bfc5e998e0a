using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Task Node - Executes specific tasks with configurable parameters.
    /// Has 1 input (trigger handle on left) and 1 output (next handle on right).
    /// Can contain multiple tasks that are executed in sequence.
    /// </summary>
    public class TaskNode : BaseFlowNode
    {
        private TaskNodeDefinition? _nodeConfig;
        private readonly Dictionary<string, Func<FlowTaskDefinition, IFlowTask>> _taskFactories;

        public TaskNode(Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>? taskFactories = null)
        {
            NodeType = "Task";
            _taskFactories = taskFactories ?? new Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>();
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left (trigger)
            _inputHandles.Add(new NodeHandle
            {
                Id = "trigger",
                Name = "Trigger",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Single output handle on the right (next)
            _outputHandles.Add(new NodeHandle
            {
                Id = "next",
                Name = "Next",
                Type = NodeHandleType.Output,
                Position = "right"
            });

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new TaskNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata
                };

                // Extract tasks from configuration
                if (definition.Configuration.ContainsKey("tasks"))
                {
                    var tasksData = definition.Configuration["tasks"];
                    if (tasksData is List<FlowTaskDefinition> tasks)
                    {
                        _nodeConfig.Tasks = tasks;
                    }
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate tasks
            if (_nodeConfig.Tasks == null || _nodeConfig.Tasks.Count == 0)
            {
                result.Warnings.Add("Task node has no tasks configured");
                return;
            }

            if (_nodeConfig.Tasks.Count > 50)
            {
                result.Warnings.Add("Task node has many tasks, consider performance implications");
            }

            // Validate each task
            for (int i = 0; i < _nodeConfig.Tasks.Count; i++)
            {
                var task = _nodeConfig.Tasks[i];
                
                if (string.IsNullOrEmpty(task.Id))
                {
                    result.Errors.Add($"Task {i} must have an ID");
                }

                if (string.IsNullOrEmpty(task.Type))
                {
                    result.Errors.Add($"Task {i} must have a type");
                }

                if (string.IsNullOrEmpty(task.Name))
                {
                    result.Warnings.Add($"Task {i} should have a descriptive name");
                }

                // Check if task type is supported
                if (!_taskFactories.ContainsKey(task.Type))
                {
                    result.Errors.Add($"Task {i} has unsupported type: {task.Type}");
                }
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                if (_nodeConfig.Tasks == null || _nodeConfig.Tasks.Count == 0)
                {
                    return CreateSuccessResult();
                }

                var taskResults = new List<TaskExecutionResult>();
                var allOutputData = new Dictionary<string, object>();
                bool anyTaskFailed = false;
                bool anyTaskAffectsFlow = false;

                // Execute tasks in sequence
                foreach (var taskDef in _nodeConfig.Tasks)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return new NodeExecutionResult
                        {
                            NodeId = NodeId,
                            Status = NodeExecutionStatus.Cancelled,
                            CompletedAt = DateTime.UtcNow,
                            ExecutionTime = DateTime.UtcNow - startTime
                        };
                    }

                    try
                    {
                        var taskResult = await ExecuteTaskAsync(taskDef, context, cancellationToken);
                        taskResults.Add(taskResult);

                        // Merge task output data
                        foreach (var kvp in taskResult.OutputData)
                        {
                            allOutputData[$"task_{taskDef.Id}_{kvp.Key}"] = kvp.Value;
                        }

                        // Check if task failed
                        if (taskResult.Status == TaskExecutionStatus.Failed)
                        {
                            anyTaskFailed = true;
                            // Decide whether to continue or stop based on task configuration
                            // For now, we'll continue execution but mark the overall result as failed
                        }

                        // Check if task affects flow execution
                        if (taskResult.AffectsFlowExecution)
                        {
                            anyTaskAffectsFlow = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        var failedTaskResult = new TaskExecutionResult
                        {
                            TaskId = taskDef.Id,
                            Status = TaskExecutionStatus.Failed,
                            ErrorMessage = ex.Message,
                            Exception = ex,
                            CompletedAt = DateTime.UtcNow
                        };
                        taskResults.Add(failedTaskResult);
                        anyTaskFailed = true;
                    }
                }

                // Prepare output data
                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "task",
                    ["taskCount"] = _nodeConfig.Tasks.Count,
                    ["executedTaskCount"] = taskResults.Count,
                    ["failedTaskCount"] = taskResults.Count(t => t.Status == TaskExecutionStatus.Failed),
                    ["taskResults"] = taskResults.Select(t => new
                    {
                        taskId = t.TaskId,
                        status = t.Status.ToString(),
                        executionTime = t.ExecutionTime.TotalMilliseconds
                    }).ToList()
                };

                // Merge all task output data
                foreach (var kvp in allOutputData)
                {
                    outputData[kvp.Key] = kvp.Value;
                }

                // Store task execution information in context
                context.ExecutionData["taskNodeId"] = NodeId;
                context.ExecutionData["taskResults"] = taskResults;
                context.ExecutionData["taskAffectsFlow"] = anyTaskAffectsFlow;

                // Determine overall result status
                NodeExecutionStatus overallStatus = anyTaskFailed ? NodeExecutionStatus.Failed : NodeExecutionStatus.Completed;

                var result = new NodeExecutionResult
                {
                    NodeId = NodeId,
                    Status = overallStatus,
                    OutputData = outputData,
                    ExecutionTime = DateTime.UtcNow - startTime,
                    CompletedAt = DateTime.UtcNow
                };

                // Add metadata about task execution
                result.Metadata["executionMode"] = "task";
                result.Metadata["taskCount"] = _nodeConfig.Tasks.Count;
                result.Metadata["anyTaskFailed"] = anyTaskFailed;
                result.Metadata["anyTaskAffectsFlow"] = anyTaskAffectsFlow;

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Task node: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a single task.
        /// </summary>
        private async Task<TaskExecutionResult> ExecuteTaskAsync(FlowTaskDefinition taskDef, FlowExecutionContext context, CancellationToken cancellationToken)
        {
            if (!_taskFactories.ContainsKey(taskDef.Type))
            {
                return new TaskExecutionResult
                {
                    TaskId = taskDef.Id,
                    Status = TaskExecutionStatus.Failed,
                    ErrorMessage = $"Unsupported task type: {taskDef.Type}",
                    CompletedAt = DateTime.UtcNow
                };
            }

            try
            {
                var taskFactory = _taskFactories[taskDef.Type];
                var task = taskFactory(taskDef);

                if (!await task.InitializeAsync(taskDef))
                {
                    return new TaskExecutionResult
                    {
                        TaskId = taskDef.Id,
                        Status = TaskExecutionStatus.Failed,
                        ErrorMessage = "Task initialization failed",
                        CompletedAt = DateTime.UtcNow
                    };
                }

                // Execute the task
                if (task.IsLongRunning)
                {
                    // For long-running tasks, start execution and return immediately
                    var sessionId = await task.StartLongRunningExecutionAsync(context);
                    return new TaskExecutionResult
                    {
                        TaskId = taskDef.Id,
                        Status = TaskExecutionStatus.LongRunning,
                        OutputData = new Dictionary<string, object> { ["sessionId"] = sessionId },
                        CompletedAt = DateTime.UtcNow
                    };
                }
                else
                {
                    // For regular tasks, execute synchronously
                    return await task.ExecuteAsync(context, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                return new TaskExecutionResult
                {
                    TaskId = taskDef.Id,
                    Status = TaskExecutionStatus.Failed,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// Registers a task factory for a specific task type.
        /// </summary>
        public void RegisterTaskFactory(string taskType, Func<FlowTaskDefinition, IFlowTask> factory)
        {
            _taskFactories[taskType] = factory;
        }

        /// <summary>
        /// Gets the configured tasks.
        /// </summary>
        public List<FlowTaskDefinition> GetTasks()
        {
            return _nodeConfig?.Tasks ?? new List<FlowTaskDefinition>();
        }

        /// <summary>
        /// Gets the number of configured tasks.
        /// </summary>
        public int GetTaskCount()
        {
            return _nodeConfig?.Tasks?.Count ?? 0;
        }
    }
}
