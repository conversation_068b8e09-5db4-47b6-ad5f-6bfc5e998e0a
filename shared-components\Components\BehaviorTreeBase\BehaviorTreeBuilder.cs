﻿using AutoMapper;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTree;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Models.Document;
using System.Runtime.CompilerServices;

namespace shared.Components.BehaviorTreeBase
{
    public class BehaviorTreeBuilder<BT, JsonBT>
    {

        private static readonly IMapper mappingConfiguration = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<JsonBT, BT>();
            cfg.CreateMap<BT, JsonBT>();
        }).CreateMapper();

        public static JsonBT MapToData(BT src)
        {
            return (JsonBT)mappingConfiguration.Map(
                src,
                typeof(BT),
                typeof(JsonBT)
                );
        }

        public static BT MapToObject(JsonBT src)
        {
            return (BT)mappingConfiguration.Map(
                src,
                typeof(JsonBT),
                typeof(BT)
                );
        }
    }
}
