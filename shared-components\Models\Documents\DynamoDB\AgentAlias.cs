﻿using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentAlias))]
    public class AgentAlias : DynamoDBModel
    {
        public const string AccountIdAliasIndex = "AccountId-Alias-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdAliasIndex)]
        public string AgentId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Alias { get; set; } = string.Empty;
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdAliasIndex)]
        public string AccountId {  get; set; } = string.Empty;
        public string AgentTag { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(AgentAlias.AgentId);
            }else if(indexName == AccountIdAliasIndex)
            {
                return nameof(AgentAlias.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(AgentAlias.Alias);
            }
            else if (indexName == AccountIdAliasIndex)
            {
                return nameof(AgentAlias.Alias);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentAlias);
        }
    }
}
