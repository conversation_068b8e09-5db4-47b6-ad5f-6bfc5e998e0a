﻿using Amazon.Runtime.Internal.Transform;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTree;
using shared.Components.BehaviorTreeBase;
using shared.Components.BehaviorTreeBase.Models;
using shared.Extension;

namespace shared.Components.AgentBehaviorTree
{
    public class AgentBehaviorTree : BehaviorTree<AgentTreeState, AgentTreeEvaluationResult>
    {
        public readonly Dictionary<string, Func<string>> Functions = new Dictionary<string, Func<string>>()
        {
            { "ts", new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString }
        };

        public string PromptTemplate { get; set; } = string.Empty;
        public AgentReference DefaultAgent {  get; set; } = new AgentReference();

        public override async Task<AgentTreeEvaluationResult> Evaluate()
        {
            AgentTreeEvaluationResult res = await base.Evaluate();
            AgentTreeState tstate = ((AgentTreeState)this.TreeState);

            if (res.TreeNodesState != BehaviorTreeBase.Enum.NodeState.SUCCESS && res.TreeNodesState != BehaviorTreeBase.Enum.NodeState.FAILURE)
            {
                return res;
            }

            if (!tstate.Result.AgentReference.IsSet())
            {
                res.Agent = DefaultAgent;
            }
            else
            {
                res.Agent = tstate.Result.AgentReference.Value ?? DefaultAgent;
            }

            var promptsStrings = new Dictionary<string, string>();
            tstate.Result.Prompt.Prompts.ToList().ForEach(k => promptsStrings.Add(k.Key, k.Value.Value ?? string.Empty));
            res.Prompt = PromptTemplate.FormatFromDictionary(promptsStrings, "!");
            res.Prompt = res.Prompt.FormatFromDictionary(tstate.Inputs, "%");
            res.Prompt = res.Prompt.FormatFromDictionary(tstate.Variables, "@");
            res.Prompt = res.Prompt.FormatFromDictionary(new Dictionary<string, string>() { { "userInput", tstate.UserInput } }, "#", cleanMissing: false);
            res.Prompt = res.Prompt.FormatFromDictionary(Functions, "#");

            return res;
        }

    }
}
