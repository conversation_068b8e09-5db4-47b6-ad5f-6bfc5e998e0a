﻿using shared.Extensions;
using shared.Models.Enums;

namespace shared.Models.Configuration
{
    public class MicroserviceConfiguration
    {
        public string Environment { get; set; } = string.Empty;
        public string InstanceId { get; set; } = string.Empty;
        public MicroserviceType Type { get; set; }
        public string Group { get; set; } = "default";
        public string FullId { get {  return $"{Type.DisplayName()}:{Group}:{InstanceId}"; } }
    }
}
