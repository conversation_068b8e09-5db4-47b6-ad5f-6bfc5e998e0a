using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Core engine interface for executing flows. Handles node traversal, state management,
    /// and execution coordination.
    /// </summary>
    public interface IFlowEngine
    {
        /// <summary>
        /// Executes a flow from start to completion.
        /// </summary>
        /// <param name="flow">The flow to execute</param>
        /// <param name="context">Execution context with initial state</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Flow execution result</returns>
        Task<FlowExecutionResult> ExecuteFlowAsync(Flow flow, FlowExecutionContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts a flow execution session that can be monitored and controlled.
        /// </summary>
        /// <param name="flow">The flow to execute</param>
        /// <param name="context">Execution context with initial state</param>
        /// <returns>Execution session ID</returns>
        Task<string> StartFlowExecutionSessionAsync(Flow flow, FlowExecutionContext context);

        /// <summary>
        /// Continues execution of a flow session from a specific point.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Updated execution result</returns>
        Task<FlowExecutionResult> ContinueExecutionAsync(string sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current execution state of a flow session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>Current execution state</returns>
        Task<FlowExecutionState> GetExecutionStateAsync(string sessionId);

        /// <summary>
        /// Pauses a running flow execution.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>True if pause was successful</returns>
        Task<bool> PauseExecutionAsync(string sessionId);

        /// <summary>
        /// Resumes a paused flow execution.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>True if resume was successful</returns>
        Task<bool> ResumeExecutionAsync(string sessionId);

        /// <summary>
        /// Cancels a running flow execution.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>True if cancellation was successful</returns>
        Task<bool> CancelExecutionAsync(string sessionId);

        /// <summary>
        /// Event fired when flow execution status changes.
        /// </summary>
        event EventHandler<FlowExecutionStatusChangedEventArgs>? OnExecutionStatusChanged;

        /// <summary>
        /// Event fired when a node starts executing.
        /// </summary>
        event EventHandler<NodeExecutionEventArgs>? OnNodeExecutionStarted;

        /// <summary>
        /// Event fired when a node completes execution.
        /// </summary>
        event EventHandler<NodeExecutionEventArgs>? OnNodeExecutionCompleted;

        /// <summary>
        /// Event fired when a task starts executing.
        /// </summary>
        event EventHandler<TaskExecutionEventArgs>? OnTaskExecutionStarted;

        /// <summary>
        /// Event fired when a task completes execution.
        /// </summary>
        event EventHandler<TaskExecutionEventArgs>? OnTaskExecutionCompleted;

        /// <summary>
        /// Event fired when intermediate results are available for streaming.
        /// </summary>
        event EventHandler<FlowStreamEventArgs>? OnStreamResult;
    }

    /// <summary>
    /// Event arguments for flow execution status changes.
    /// </summary>
    public class FlowExecutionStatusChangedEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public FlowExecutionStatus OldStatus { get; set; }
        public FlowExecutionStatus NewStatus { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for node execution events.
    /// </summary>
    public class NodeExecutionEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string NodeType { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public NodeExecutionResult? Result { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for task execution events.
    /// </summary>
    public class TaskExecutionEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string TaskId { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty;
        public string TaskName { get; set; } = string.Empty;
        public TaskExecutionResult? Result { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for flow streaming results.
    /// </summary>
    public class FlowStreamEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public string StreamType { get; set; } = string.Empty;
        public object Data { get; set; } = new();
        public string? SourceNodeId { get; set; }
        public string? SourceTaskId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
