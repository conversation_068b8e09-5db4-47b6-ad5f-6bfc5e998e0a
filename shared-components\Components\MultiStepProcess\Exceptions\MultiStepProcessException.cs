namespace shared.Components.MultiStepProcess.Exceptions
{
    /// <summary>
    /// Base exception for multi-step process related errors.
    /// </summary>
    public abstract class MultiStepProcessException : Exception
    {
        /// <summary>
        /// Creates a new MultiStepProcessException.
        /// </summary>
        /// <param name="message">Exception message</param>
        protected MultiStepProcessException(string message) : base(message)
        {
        }

        /// <summary>
        /// Creates a new MultiStepProcessException with inner exception.
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        protected MultiStepProcessException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when a step configuration is invalid or missing.
    /// </summary>
    public class StepConfigurationException : MultiStepProcessException
    {
        /// <summary>
        /// The state that has the configuration issue.
        /// </summary>
        public object State { get; }

        /// <summary>
        /// Creates a new StepConfigurationException.
        /// </summary>
        /// <param name="state">The state with configuration issues</param>
        /// <param name="message">Exception message</param>
        public StepConfigurationException(object state, string message) : base(message)
        {
            State = state;
        }

        /// <summary>
        /// Creates a new StepConfigurationException with inner exception.
        /// </summary>
        /// <param name="state">The state with configuration issues</param>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public StepConfigurationException(object state, string message, Exception innerException) : base(message, innerException)
        {
            State = state;
        }
    }

    /// <summary>
    /// Exception thrown when a step task execution fails.
    /// </summary>
    public class StepTaskExecutionException : MultiStepProcessException
    {
        /// <summary>
        /// The state where the task execution failed.
        /// </summary>
        public object State { get; }

        /// <summary>
        /// The process state when the failure occurred.
        /// </summary>
        public object ProcessState { get; }

        /// <summary>
        /// Creates a new StepTaskExecutionException.
        /// </summary>
        /// <param name="state">The state where execution failed</param>
        /// <param name="processState">The process state when failure occurred</param>
        /// <param name="message">Exception message</param>
        public StepTaskExecutionException(object state, object processState, string message) : base(message)
        {
            State = state;
            ProcessState = processState;
        }

        /// <summary>
        /// Creates a new StepTaskExecutionException with inner exception.
        /// </summary>
        /// <param name="state">The state where execution failed</param>
        /// <param name="processState">The process state when failure occurred</param>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public StepTaskExecutionException(object state, object processState, string message, Exception innerException) : base(message, innerException)
        {
            State = state;
            ProcessState = processState;
        }
    }

    /// <summary>
    /// Exception thrown when maximum retry limit is exceeded.
    /// </summary>
    public class MaxRetryExceededException : MultiStepProcessException
    {
        /// <summary>
        /// The state where retry limit was exceeded.
        /// </summary>
        public object State { get; }

        /// <summary>
        /// The current retry count.
        /// </summary>
        public int RetryCount { get; }

        /// <summary>
        /// The maximum allowed retries.
        /// </summary>
        public int MaxRetries { get; }

        /// <summary>
        /// Creates a new MaxRetryExceededException.
        /// </summary>
        /// <param name="state">The state where retry limit was exceeded</param>
        /// <param name="retryCount">Current retry count</param>
        /// <param name="maxRetries">Maximum allowed retries</param>
        public MaxRetryExceededException(object state, int retryCount, int maxRetries) 
            : base($"Maximum retry limit ({maxRetries}) exceeded for state '{state}'. Current retry count: {retryCount}")
        {
            State = state;
            RetryCount = retryCount;
            MaxRetries = maxRetries;
        }
    }

    /// <summary>
    /// Exception thrown when process configuration is invalid.
    /// </summary>
    public class ProcessConfigurationException : MultiStepProcessException
    {
        /// <summary>
        /// Creates a new ProcessConfigurationException.
        /// </summary>
        /// <param name="message">Exception message</param>
        public ProcessConfigurationException(string message) : base(message)
        {
        }

        /// <summary>
        /// Creates a new ProcessConfigurationException with inner exception.
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public ProcessConfigurationException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
