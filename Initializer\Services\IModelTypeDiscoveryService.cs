namespace Initializer.Services
{
    /// <summary>
    /// Interface for discovering NoSQL model types from assemblies.
    /// </summary>
    public interface IModelTypeDiscoveryService
    {
        /// <summary>
        /// Discovers all NoSQL model types that inherit from DynamoDBModel.
        /// </summary>
        /// <returns>Collection of model types</returns>
        IEnumerable<Type> DiscoverModelTypes();

        /// <summary>
        /// Discovers NoSQL model types from specific assemblies.
        /// </summary>
        /// <param name="assemblyNames">Names of assemblies to search</param>
        /// <returns>Collection of model types</returns>
        IEnumerable<Type> DiscoverModelTypes(params string[] assemblyNames);

        /// <summary>
        /// Gets all loaded assemblies that contain NoSQL models.
        /// </summary>
        /// <returns>Collection of assemblies</returns>
        IEnumerable<System.Reflection.Assembly> GetModelAssemblies();
    }
}
