﻿using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(APIKey))]
    public class APIKey : APIKeyStorageDynamoDBModel
    {

        public string Name { get; set; } = string.Empty;
        public string OwnerUserId { get; set; } = string.Empty;

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(APIKey);
        }
    }
}
