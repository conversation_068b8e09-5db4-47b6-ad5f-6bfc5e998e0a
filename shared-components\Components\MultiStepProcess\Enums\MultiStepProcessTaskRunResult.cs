using System.Text.Json.Serialization;
using shared.Converters;

namespace shared.Components.MultiStepProcess.Enums
{
    /// <summary>
    /// Represents the result of executing a state task or rollback task.
    /// Determines how the process should proceed after task execution.
    /// </summary>
    [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessTaskRunResult>))]
    public enum MultiStepProcessTaskRunResult
    {
        /// <summary>
        /// Task completed successfully. Process should transition to the next state.
        /// </summary>
        Successful,

        /// <summary>
        /// Task failed but should be retried. Process stays in current state and increments retry counter.
        /// </summary>
        FailedRetry,

        /// <summary>
        /// Task failed and process should be aborted. If in Running state, switches to RollingBack.
        /// If in RollingBack state, marks process as finished with failure.
        /// </summary>
        FailedAbort,

        /// <summary>
        /// Task timed out. Behavior depends on process configuration (treat as retry or abort).
        /// </summary>
        FailedTimeout
    }
}
