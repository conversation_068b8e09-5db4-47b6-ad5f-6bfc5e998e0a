﻿namespace shared.Types
{
    public class PriorityValue<T>
    {
        public int Priority { get; set; } = -1;
        public T? Value { get; set; } = default(T?);
        
        public bool Set(int priority, T value)
        {
            if (priority < 0) priority = 0;
            if (priority < Priority) return false;
            this.Priority = priority;
            this.Value = value;
            return true;
        }

        public bool Set(PriorityValue<T> value)
        {
            if (value.Priority < Priority) return false;
            this.Priority = value.Priority;
            this.Value = value.Value;
            return true;
        }

        public void Reset()
        {
            Priority = -1;
            Value = default(T);
        }

        public bool IsSet() { 
            return Priority >= 0 && Value != null; 
        }
    }
}
