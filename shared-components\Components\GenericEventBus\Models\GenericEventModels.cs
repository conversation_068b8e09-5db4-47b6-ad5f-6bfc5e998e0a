using System.Collections.Concurrent;

namespace shared.Components.GenericEventBus.Models
{
    /// <summary>
    /// Generic event message for the event bus system.
    /// Can be used by any component in the system for inter-component communication.
    /// </summary>
    public class GenericEventMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string MessageType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Target { get; set; } = string.Empty;
        public object Payload { get; set; } = new();
        public Dictionary<string, string> Headers { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ProcessAt { get; set; }
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public string? CorrelationId { get; set; }
        public string? SessionId { get; set; }
        public int Priority { get; set; } = 0; // Higher number = higher priority
        public TimeSpan? TimeToLive { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Message processing status.
    /// </summary>
    public enum MessageStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled,
        Retrying,
        Expired
    }

    /// <summary>
    /// Event arguments for successful message processing.
    /// </summary>
    public class MessageProcessedEventArgs : EventArgs
    {
        public string MessageId { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Target { get; set; } = string.Empty;
        public TimeSpan ProcessingTime { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for failed message processing.
    /// </summary>
    public class MessageFailedEventArgs : EventArgs
    {
        public string MessageId { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Target { get; set; } = string.Empty;
        public Exception Exception { get; set; } = new();
        public int RetryCount { get; set; }
        public bool WillRetry { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Configuration for event bus queues.
    /// </summary>
    public class EventBusQueueConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public int MaxSize { get; set; } = 1000;
        public int MaxConcurrency { get; set; } = 1;
        public TimeSpan DefaultMessageTtl { get; set; } = TimeSpan.FromHours(24);
        public bool EnableDeadLetterQueue { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);
        public bool EnablePriorityProcessing { get; set; } = false;
    }

    /// <summary>
    /// Statistics for event bus operations.
    /// </summary>
    public class EventBusStatistics
    {
        // Use fields for counters that need to be used with Interlocked operations
        public long TotalMessagesSent;
        public long TotalMessagesProcessed;
        public long TotalMessagesFailed;
        public long TotalMessagesRetried;
        public long TotalMessagesExpired;

        // Use properties for complex types and non-counter values
        public ConcurrentDictionary<string, long> MessageTypeStats { get; set; } = new();
        public ConcurrentDictionary<string, long> QueueStats { get; set; } = new();
        public TimeSpan AverageProcessingTime { get; set; }
        public DateTime LastResetTime { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Health status of the event bus.
    /// </summary>
    public class EventBusHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public Dictionary<string, object> Details { get; set; } = new();
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;
        public List<string> Issues { get; set; } = new();
    }
}
