﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using System.Text.Json;
using System.Text;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    public class DynamoDBListOfConverter<T, Converter> : IPropertyConverter where T : notnull where Converter : IPropertyConverter, new()
    {
        private readonly Converter converter = new Converter();

        public object? FromEntry(DynamoDBEntry entry)
        {
            var docs = entry.AsListOfDocument();
            List<T> result = new List<T>();

            foreach (var doc in docs)
            {
                result.Add((T)converter.FromEntry(doc));
            }

            return result;
        }

        public DynamoDBEntry ToEntry(object value)
        {
            var entries = value as List<T> ?? new List<T>();
            DynamoDBList dynamoDBList = new DynamoDBList();
            foreach(var entry in entries){
                dynamoDBList.Add(converter.ToEntry(entry));
            }
            return dynamoDBList;
        }
    }
}
