﻿using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;

namespace shared.Components.BehaviorTreeBase.Tasks
{
    public class DummyTask : Node
    {

        public DummyTask(string nodeId, List<Node>? children, ConditionExpression? conditionExpression) : base(nodeId, children, conditionExpression)
        {
        }

        protected override Task<NodeState> EvaluateImpl(TreeState treeState)
        {
            return Task.FromResult(NodeState.SUCCESS);
        }
    }
}
