using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.Model;
using shared.Models.Documents;
using shared.Models.Documents.DynamoDB;
using System.Reflection;

namespace shared.Services.Implementation
{
    /// <summary>
    /// DynamoDB implementation of the NoSQL table manager interface.
    /// Handles table creation, validation, and deletion operations.
    /// </summary>
    /// <typeparam name="T">Model type that extends DynamoDBModel</typeparam>
    public class DynamoDBTableManager<T> : INoSQLTableManager<T> where T : DynamoDBModel, new()
    {
        private readonly IAmazonDynamoDB _dynamoClient;

        public DynamoDBTableManager(IAmazonDynamoDB dynamoClient)
        {
            _dynamoClient = dynamoClient;
        }

        /// <summary>
        /// Creates a table for the model type with all primary keys and secondary indexes.
        /// Automatically detects and creates GSI and LSI indexes based on model attributes.
        /// </summary>
        public async Task<bool> CreateTableAsync()
        {
            try
            {
                var tableName = GetTableName();
                
                // Check if table already exists
                if (await TableExistsAsync())
                    return true;

                var createRequest = new CreateTableRequest
                {
                    TableName = tableName,
                    BillingMode = BillingMode.PAY_PER_REQUEST
                };

                // Add primary key schema and attribute definitions
                AddPrimaryKeySchemaAndAttributes(createRequest);

                // Add secondary indexes
                AddSecondaryIndexes(createRequest);

                await _dynamoClient.CreateTableAsync(createRequest);
                
                // Wait for table to be active
                await WaitForTableToBeActive(tableName);
                
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return false;
            }
        }

        /// <summary>
        /// Deletes a table for the model type.
        /// </summary>
        public async Task<bool> DeleteTableAsync()
        {
            try
            {
                var tableName = GetTableName();

                // Check if table exists before attempting deletion
                if (!await TableExistsAsync())
                {
                    return true; // Table doesn't exist, consider deletion successful
                }

                await _dynamoClient.DeleteTableAsync(tableName);

                // Wait for table to be fully deleted
                await WaitForTableToBeDeleted(tableName);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if table exists for the model type.
        /// </summary>
        public async Task<bool> TableExistsAsync()
        {
            try
            {
                var tableName = GetTableName();
                var response = await _dynamoClient.DescribeTableAsync(tableName);
                return response.Table.TableStatus == TableStatus.ACTIVE;
            }
            catch (ResourceNotFoundException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Validates that the existing table matches the model's configuration.
        /// Checks primary keys, secondary indexes, and their key schemas.
        /// </summary>
        public async Task<TableValidationResult> ValidateTableAsync()
        {
            var result = new TableValidationResult();

            try
            {
                var tableName = GetTableName();

                // Check if table exists
                DescribeTableResponse tableDescription;
                try
                {
                    tableDescription = await _dynamoClient.DescribeTableAsync(tableName);
                    result.TableExists = true;
                    result.TableStatus = tableDescription.Table.TableStatus.Value;
                }
                catch (ResourceNotFoundException)
                {
                    result.TableExists = false;
                    result.ValidationErrors.Add($"Table '{tableName}' does not exist");
                    result.IsValid = false;
                    return result;
                }

                var table = tableDescription.Table;

                // Check table status - only validate if table is in ACTIVE state
                if (table.TableStatus != TableStatus.ACTIVE)
                {
                    result.IsValid = false;
                    result.ValidationErrors.Add($"Table '{tableName}' is not in ACTIVE state. Current status: {table.TableStatus}");

                    // If table is in transitional state, don't perform detailed validation
                    if (table.TableStatus == TableStatus.CREATING ||
                        table.TableStatus == TableStatus.UPDATING ||
                        table.TableStatus == TableStatus.DELETING)
                    {
                        result.ValidationErrors.Add($"Table '{tableName}' is in transitional state. Validation skipped.");
                        return result;
                    }
                }

                // Validate primary key
                result.PrimaryKey = ValidatePrimaryKey(table);
                if (!result.PrimaryKey.IsValid)
                {
                    result.IsValid = false;
                    result.ValidationErrors.AddRange(result.PrimaryKey.ValidationErrors);
                }

                // Validate secondary indexes
                result.SecondaryIndexes = ValidateSecondaryIndexes(table);
                foreach (var indexResult in result.SecondaryIndexes)
                {
                    if (!indexResult.IsValid)
                    {
                        result.IsValid = false;
                        result.ValidationErrors.AddRange(indexResult.ValidationErrors);
                    }
                }

                // Validate billing mode if table is active
                if (table.TableStatus == TableStatus.ACTIVE)
                {
                    ValidateBillingMode(table, result);
                }

                // If no errors found and table is active, table is valid
                if (result.ValidationErrors.Count == 0 && table.TableStatus == TableStatus.ACTIVE)
                {
                    result.IsValid = true;
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ValidationErrors.Add($"Error validating table: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Gets the table name for the model type.
        /// </summary>
        public string GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(T));
        }

        /// <summary>
        /// Gets detailed information about the table schema expected by the model.
        /// </summary>
        public async Task<TableSchemaInfo> GetExpectedTableSchemaAsync()
        {
            var schema = new TableSchemaInfo
            {
                TableName = GetTableName(),
                HashKeyAttribute = GetHashKeyPropertyName(),
                RangeKeyAttribute = GetRangeKeyPropertyName()
            };

            var indexConfigurations = GetSecondaryIndexConfigurations();
            
            foreach (var indexConfig in indexConfigurations)
            {
                var indexInfo = new SecondaryIndexInfo
                {
                    IndexName = indexConfig.IndexName,
                    IndexType = indexConfig.IndexType,
                    HashKeyAttribute = indexConfig.HashKeyAttribute,
                    RangeKeyAttribute = indexConfig.RangeKeyAttribute
                };

                if (indexConfig.IndexType == "GSI")
                {
                    schema.GlobalSecondaryIndexes.Add(indexInfo);
                }
                else
                {
                    schema.LocalSecondaryIndexes.Add(indexInfo);
                }
            }

            // Collect all unique attributes
            var attributes = new HashSet<(string Name, string Type)>();
            
            if (schema.HashKeyAttribute != null)
                attributes.Add((schema.HashKeyAttribute, GetAttributeTypeForProperty(schema.HashKeyAttribute)));
            
            if (schema.RangeKeyAttribute != null)
                attributes.Add((schema.RangeKeyAttribute, GetAttributeTypeForProperty(schema.RangeKeyAttribute)));

            foreach (var index in indexConfigurations)
            {
                attributes.Add((index.HashKeyAttribute, index.HashKeyType));
                if (index.RangeKeyAttribute != null)
                    attributes.Add((index.RangeKeyAttribute, index.RangeKeyType ?? "S"));
            }

            schema.AttributeDefinitions = attributes.Select(attr => new AttributeInfo
            {
                AttributeName = attr.Name,
                AttributeType = attr.Type
            }).ToList();

            return schema;
        }

        #region Private Helper Methods

        /// <summary>
        /// Gets hash key property name for the model type.
        /// </summary>
        private static string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(T), indexName);
        }

        /// <summary>
        /// Gets range key property name for the model type.
        /// </summary>
        private static string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(T), indexName);
        }

        /// <summary>
        /// Adds primary key schema and attribute definitions to create table request.
        /// </summary>
        private void AddPrimaryKeySchemaAndAttributes(CreateTableRequest request)
        {
            var hashKeyProperty = GetHashKeyPropertyName();
            var rangeKeyProperty = GetRangeKeyPropertyName();

            if (request.KeySchema == null) request.KeySchema = new List<KeySchemaElement>();

            if (hashKeyProperty != null)
            {
                request.KeySchema.Add(new KeySchemaElement
                {
                    AttributeName = hashKeyProperty,
                    KeyType = KeyType.HASH
                });

                AddAttributeDefinitionIfNotExists(request, hashKeyProperty, GetAttributeTypeForProperty(hashKeyProperty));
            }

            if (rangeKeyProperty != null)
            {
                request.KeySchema.Add(new KeySchemaElement
                {
                    AttributeName = rangeKeyProperty,
                    KeyType = KeyType.RANGE
                });

                AddAttributeDefinitionIfNotExists(request, rangeKeyProperty, GetAttributeTypeForProperty(rangeKeyProperty));
            }
        }

        /// <summary>
        /// Adds secondary indexes (GSI and LSI) to the create table request.
        /// </summary>
        private void AddSecondaryIndexes(CreateTableRequest request)
        {
            var indexConfigurations = GetSecondaryIndexConfigurations();

            foreach (var indexConfig in indexConfigurations)
            {
                if (indexConfig.IndexType == "GSI")
                {
                    AddGlobalSecondaryIndex(request, indexConfig);
                }
                else if (indexConfig.IndexType == "LSI")
                {
                    AddLocalSecondaryIndex(request, indexConfig);
                }
            }
        }

        /// <summary>
        /// Waits for table to become active.
        /// </summary>
        private async Task WaitForTableToBeActive(string tableName)
        {
            var maxWaitTime = TimeSpan.FromMinutes(10); // Increased timeout for better reliability
            var startTime = DateTime.UtcNow;
            var lastStatus = "UNKNOWN";

            while (DateTime.UtcNow - startTime < maxWaitTime)
            {
                try
                {
                    var response = await _dynamoClient.DescribeTableAsync(tableName);
                    var currentStatus = response.Table.TableStatus.Value;

                    if (currentStatus != lastStatus)
                    {
                        // Log status changes for better visibility
                        lastStatus = currentStatus;
                    }

                    if (response.Table.TableStatus == TableStatus.ACTIVE)
                    {
                        // Also check that all GSI indexes are active
                        if (response.Table.GlobalSecondaryIndexes != null)
                        {
                            var inactiveIndexes = response.Table.GlobalSecondaryIndexes
                                .Where(gsi => gsi.IndexStatus != IndexStatus.ACTIVE)
                                .ToList();

                            if (inactiveIndexes.Any())
                            {
                                // Wait for GSI indexes to become active
                                await Task.Delay(TimeSpan.FromSeconds(5));
                                continue;
                            }
                        }

                        return; // Table and all indexes are active
                    }

                    // Check for error states
                    if (response.Table.TableStatus == TableStatus.INACCESSIBLE_ENCRYPTION_CREDENTIALS ||
                        response.Table.TableStatus == TableStatus.ARCHIVING ||
                        response.Table.TableStatus == TableStatus.ARCHIVED)
                    {
                        throw new InvalidOperationException($"Table {tableName} is in an error state: {response.Table.TableStatus}");
                    }
                }
                catch (ResourceNotFoundException)
                {
                    // Table doesn't exist yet, continue waiting
                }
                catch (Exception ex) when (!(ex is InvalidOperationException))
                {
                    // Continue waiting for other exceptions, but not for our custom InvalidOperationException
                }

                await Task.Delay(TimeSpan.FromSeconds(5));
            }

            throw new TimeoutException($"Table {tableName} did not become active within {maxWaitTime.TotalMinutes} minutes. Last known status: {lastStatus}");
        }

        /// <summary>
        /// Waits for table to be fully deleted.
        /// </summary>
        private async Task WaitForTableToBeDeleted(string tableName)
        {
            var maxWaitTime = TimeSpan.FromMinutes(10);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < maxWaitTime)
            {
                try
                {
                    var response = await _dynamoClient.DescribeTableAsync(tableName);

                    // If we can still describe the table, it's not fully deleted yet
                    if (response.Table.TableStatus == TableStatus.DELETING)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(5));
                        continue;
                    }

                    // If table is in any other state, something went wrong
                    throw new InvalidOperationException($"Table {tableName} is in unexpected state during deletion: {response.Table.TableStatus}");
                }
                catch (ResourceNotFoundException)
                {
                    // Table no longer exists, deletion is complete
                    return;
                }
                catch (Exception ex) when (!(ex is InvalidOperationException))
                {
                    // For other exceptions, continue waiting
                }

                await Task.Delay(TimeSpan.FromSeconds(5));
            }

            throw new TimeoutException($"Table {tableName} was not fully deleted within {maxWaitTime.TotalMinutes} minutes");
        }

        /// <summary>
        /// Adds a Global Secondary Index to the create table request.
        /// </summary>
        private void AddGlobalSecondaryIndex(CreateTableRequest request, IndexConfiguration indexConfig)
        {
            var gsi = new GlobalSecondaryIndex
            {
                IndexName = indexConfig.IndexName,
                Projection = new Projection { ProjectionType = ProjectionType.ALL }
            };

            // Add hash key
            if (gsi.KeySchema == null) gsi.KeySchema = new List<KeySchemaElement>();
            gsi.KeySchema.Add(new KeySchemaElement
            {
                AttributeName = indexConfig.HashKeyAttribute,
                KeyType = KeyType.HASH
            });

            AddAttributeDefinitionIfNotExists(request, indexConfig.HashKeyAttribute, indexConfig.HashKeyType);

            // Add range key if present
            if (!string.IsNullOrEmpty(indexConfig.RangeKeyAttribute))
            {
                gsi.KeySchema.Add(new KeySchemaElement
                {
                    AttributeName = indexConfig.RangeKeyAttribute,
                    KeyType = KeyType.RANGE
                });

                AddAttributeDefinitionIfNotExists(request, indexConfig.RangeKeyAttribute, indexConfig.RangeKeyType ?? "S");
            }

            // Set provisioned throughput for GSI (required even with PAY_PER_REQUEST billing mode)
            /*gsi.ProvisionedThroughput = new ProvisionedThroughput
            {
                ReadCapacityUnits = 1,
                WriteCapacityUnits = 1
            };**/

            if(request.GlobalSecondaryIndexes == null) request.GlobalSecondaryIndexes = new List<GlobalSecondaryIndex>();
            request.GlobalSecondaryIndexes.Add(gsi);
        }

        /// <summary>
        /// Adds a Local Secondary Index to the create table request.
        /// </summary>
        private void AddLocalSecondaryIndex(CreateTableRequest request, IndexConfiguration indexConfig)
        {
            var lsi = new LocalSecondaryIndex
            {
                IndexName = indexConfig.IndexName,
                Projection = new Projection { ProjectionType = ProjectionType.ALL }
            };

            // LSI uses the same hash key as the table
            if (lsi.KeySchema == null) lsi.KeySchema = new List<KeySchemaElement>();
            lsi.KeySchema.Add(new KeySchemaElement
            {
                AttributeName = indexConfig.HashKeyAttribute,
                KeyType = KeyType.HASH
            });

            // Add range key
            if (!string.IsNullOrEmpty(indexConfig.RangeKeyAttribute))
            {
                lsi.KeySchema.Add(new KeySchemaElement
                {
                    AttributeName = indexConfig.RangeKeyAttribute,
                    KeyType = KeyType.RANGE
                });

                AddAttributeDefinitionIfNotExists(request, indexConfig.RangeKeyAttribute, indexConfig.RangeKeyType ?? "S");
            }

            request.LocalSecondaryIndexes.Add(lsi);
        }

        /// <summary>
        /// Adds an attribute definition if it doesn't already exist.
        /// </summary>
        private void AddAttributeDefinitionIfNotExists(CreateTableRequest request, string attributeName, string attributeType)
        {
            if (request.AttributeDefinitions == null) request.AttributeDefinitions = new List<AttributeDefinition>();
            if (!request.AttributeDefinitions.Any(ad => ad.AttributeName == attributeName))
            {
                request.AttributeDefinitions.Add(new AttributeDefinition
                {
                    AttributeName = attributeName,
                    AttributeType = attributeType
                });
            }
        }

        /// <summary>
        /// Gets the DynamoDB attribute type for a property by name.
        /// </summary>
        private string GetAttributeTypeForProperty(string propertyName)
        {
            var property = typeof(T).GetProperty(propertyName);
            return property != null ? GetAttributeType(property.PropertyType) : "S";
        }

        /// <summary>
        /// Validates the primary key configuration.
        /// </summary>
        private KeyValidationResult ValidatePrimaryKey(TableDescription table)
        {
            var result = new KeyValidationResult();

            // Get expected keys from model
            result.ExpectedHashKey = GetHashKeyPropertyName();
            result.ExpectedRangeKey = GetRangeKeyPropertyName();

            // Get actual keys from table
            var hashKeyElement = table.KeySchema.FirstOrDefault(ks => ks.KeyType == KeyType.HASH);
            var rangeKeyElement = table.KeySchema.FirstOrDefault(ks => ks.KeyType == KeyType.RANGE);

            result.ActualHashKey = hashKeyElement?.AttributeName;
            result.ActualRangeKey = rangeKeyElement?.AttributeName;

            // Validate hash key
            if (result.ExpectedHashKey != result.ActualHashKey)
            {
                result.ValidationErrors.Add($"Hash key mismatch. Expected: '{result.ExpectedHashKey}', Actual: '{result.ActualHashKey}'");
            }

            // Validate range key
            if (result.ExpectedRangeKey != result.ActualRangeKey)
            {
                result.ValidationErrors.Add($"Range key mismatch. Expected: '{result.ExpectedRangeKey}', Actual: '{result.ActualRangeKey}'");
            }

            result.IsValid = result.ValidationErrors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates all secondary indexes.
        /// </summary>
        private List<IndexValidationResult> ValidateSecondaryIndexes(TableDescription table)
        {
            var results = new List<IndexValidationResult>();
            var expectedIndexes = GetSecondaryIndexConfigurations();

            // Validate each expected index
            foreach (var expectedIndex in expectedIndexes)
            {
                var result = new IndexValidationResult
                {
                    IndexName = expectedIndex.IndexName,
                    IndexType = expectedIndex.IndexType,
                    ExpectedHashKey = expectedIndex.HashKeyAttribute,
                    ExpectedRangeKey = expectedIndex.RangeKeyAttribute
                };

                if (expectedIndex.IndexType == "GSI")
                {
                    var actualGsi = table.GlobalSecondaryIndexes?.FirstOrDefault(gsi => gsi.IndexName == expectedIndex.IndexName);
                    if (actualGsi != null)
                    {
                        result.IndexExists = true;
                        result.IndexStatus = actualGsi.IndexStatus?.Value;
                        ValidateIndexKeys(result, actualGsi.KeySchema);
                    }
                    else
                    {
                        result.IndexExists = false;
                        result.ValidationErrors.Add($"Global Secondary Index '{expectedIndex.IndexName}' does not exist");
                    }
                }
                else if (expectedIndex.IndexType == "LSI")
                {
                    var actualLsi = table.LocalSecondaryIndexes?.FirstOrDefault(lsi => lsi.IndexName == expectedIndex.IndexName);
                    if (actualLsi != null)
                    {
                        result.IndexExists = true;
                        result.IndexStatus = "ACTIVE"; // LSI doesn't have separate status
                        ValidateIndexKeys(result, actualLsi.KeySchema);
                    }
                    else
                    {
                        result.IndexExists = false;
                        result.ValidationErrors.Add($"Local Secondary Index '{expectedIndex.IndexName}' does not exist");
                    }
                }

                result.IsValid = result.ValidationErrors.Count == 0;
                results.Add(result);
            }

            // Check for unexpected indexes in the table
            if (table.GlobalSecondaryIndexes != null)
            {
                foreach (var actualGsi in table.GlobalSecondaryIndexes)
                {
                    if (!expectedIndexes.Any(ei => ei.IndexName == actualGsi.IndexName && ei.IndexType == "GSI"))
                    {
                        var result = new IndexValidationResult
                        {
                            IndexName = actualGsi.IndexName,
                            IndexType = "GSI",
                            IndexExists = true,
                            IndexStatus = actualGsi.IndexStatus?.Value,
                            IsValid = false
                        };
                        result.ValidationErrors.Add($"Unexpected Global Secondary Index '{actualGsi.IndexName}' found in table");
                        results.Add(result);
                    }
                }
            }

            if (table.LocalSecondaryIndexes != null)
            {
                foreach (var actualLsi in table.LocalSecondaryIndexes)
                {
                    if (!expectedIndexes.Any(ei => ei.IndexName == actualLsi.IndexName && ei.IndexType == "LSI"))
                    {
                        var result = new IndexValidationResult
                        {
                            IndexName = actualLsi.IndexName,
                            IndexType = "LSI",
                            IndexExists = true,
                            IndexStatus = "ACTIVE",
                            IsValid = false
                        };
                        result.ValidationErrors.Add($"Unexpected Local Secondary Index '{actualLsi.IndexName}' found in table");
                        results.Add(result);
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// Validates the key schema for an index.
        /// </summary>
        private void ValidateIndexKeys(IndexValidationResult result, List<KeySchemaElement> actualKeySchema)
        {
            var actualHashKey = actualKeySchema.FirstOrDefault(ks => ks.KeyType == KeyType.HASH)?.AttributeName;
            var actualRangeKey = actualKeySchema.FirstOrDefault(ks => ks.KeyType == KeyType.RANGE)?.AttributeName;

            result.ActualHashKey = actualHashKey;
            result.ActualRangeKey = actualRangeKey;

            // Validate hash key
            if (result.ExpectedHashKey != result.ActualHashKey)
            {
                result.ValidationErrors.Add($"Index '{result.IndexName}' hash key mismatch. Expected: '{result.ExpectedHashKey}', Actual: '{result.ActualHashKey}'");
            }

            // Validate range key
            if (result.ExpectedRangeKey != result.ActualRangeKey)
            {
                result.ValidationErrors.Add($"Index '{result.IndexName}' range key mismatch. Expected: '{result.ExpectedRangeKey}', Actual: '{result.ActualRangeKey}'");
            }
        }

        /// <summary>
        /// Validates the billing mode configuration.
        /// </summary>
        private void ValidateBillingMode(TableDescription table, TableValidationResult result)
        {
            // For now, we expect PAY_PER_REQUEST billing mode
            // This can be made configurable in the future
            var expectedBillingMode = BillingMode.PAY_PER_REQUEST;

            if (table.BillingModeSummary?.BillingMode != expectedBillingMode)
            {
                result.ValidationErrors.Add($"Billing mode mismatch. Expected: '{expectedBillingMode}', Actual: '{table.BillingModeSummary?.BillingMode}'");
            }
        }

        
        #region Index Detection Helper Methods

        /// <summary>
        /// Represents a secondary index configuration.
        /// </summary>
        private class IndexConfiguration
        {
            public string IndexName { get; set; } = string.Empty;
            public string IndexType { get; set; } = string.Empty; // "GSI" or "LSI"
            public string HashKeyAttribute { get; set; } = string.Empty;
            public string? RangeKeyAttribute { get; set; }
            public string HashKeyType { get; set; } = "S"; // S, N, B
            public string? RangeKeyType { get; set; }
        }

        /// <summary>
        /// Gets all secondary index configurations from the model type.
        /// </summary>
        private static List<IndexConfiguration> GetSecondaryIndexConfigurations()
        {
            var indexes = new List<IndexConfiguration>();
            var modelType = typeof(T);
            var properties = modelType.GetProperties();

            // Track all index names we've seen
            var indexNames = new HashSet<string>();

            // First pass: collect all index names
            foreach (var property in properties)
            {
                // GSI Hash Keys
                var gsiHashAttrs = property.GetCustomAttributes<DynamoDBGlobalSecondaryIndexHashKeyAttribute>();
                foreach (var attr in gsiHashAttrs)
                {
                    if (attr.IndexNames != null)
                    {
                        foreach (var indexName in attr.IndexNames)
                        {
                            indexNames.Add(indexName);
                        }
                    }
                }

                // GSI Range Keys
                var gsiRangeAttrs = property.GetCustomAttributes<DynamoDBGlobalSecondaryIndexRangeKeyAttribute>();
                foreach (var attr in gsiRangeAttrs)
                {
                    if (attr.IndexNames != null)
                    {
                        foreach (var indexName in attr.IndexNames)
                        {
                            indexNames.Add(indexName);
                        }
                    }
                }

                // LSI Range Keys
                var lsiRangeAttrs = property.GetCustomAttributes<DynamoDBLocalSecondaryIndexRangeKeyAttribute>();
                foreach (var attr in lsiRangeAttrs)
                {
                    if (attr.IndexNames != null)
                    {
                        foreach (var indexName in attr.IndexNames)
                        {
                            indexNames.Add(indexName);
                        }
                    }
                }
            }

            // Second pass: build index configurations
            foreach (var indexName in indexNames)
            {
                var indexConfig = new IndexConfiguration { IndexName = indexName };

                // Determine if this is GSI or LSI by checking for GSI hash key
                bool isGSI = false;

                foreach (var property in properties)
                {
                    // Check for GSI hash key
                    var gsiHashAttrs = property.GetCustomAttributes<DynamoDBGlobalSecondaryIndexHashKeyAttribute>();
                    foreach (var attr in gsiHashAttrs)
                    {
                        if (attr.IndexNames?.Contains(indexName) == true)
                        {
                            indexConfig.IndexType = "GSI";
                            indexConfig.HashKeyAttribute = property.Name;
                            indexConfig.HashKeyType = GetAttributeType(property.PropertyType);
                            isGSI = true;
                            break;
                        }
                    }

                    if (isGSI) break;
                }

                // If not GSI, it must be LSI (uses table hash key)
                if (!isGSI)
                {
                    indexConfig.IndexType = "LSI";
                    var tableHashKeyProperty = NoSQLModel.GetPropertyByAttribute(modelType, typeof(DynamoDBHashKeyAttribute));
                    if (tableHashKeyProperty != null)
                    {
                        indexConfig.HashKeyAttribute = tableHashKeyProperty.Name;
                        indexConfig.HashKeyType = GetAttributeType(tableHashKeyProperty.PropertyType);
                    }
                }

                // Find range key for this index
                foreach (var property in properties)
                {
                    if (indexConfig.IndexType == "GSI")
                    {
                        var gsiRangeAttrs = property.GetCustomAttributes<DynamoDBGlobalSecondaryIndexRangeKeyAttribute>();
                        foreach (var attr in gsiRangeAttrs)
                        {
                            if (attr.IndexNames?.Contains(indexName) == true)
                            {
                                indexConfig.RangeKeyAttribute = property.Name;
                                indexConfig.RangeKeyType = GetAttributeType(property.PropertyType);
                                break;
                            }
                        }
                    }
                    else // LSI
                    {
                        var lsiRangeAttrs = property.GetCustomAttributes<DynamoDBLocalSecondaryIndexRangeKeyAttribute>();
                        foreach (var attr in lsiRangeAttrs)
                        {
                            if (attr.IndexNames?.Contains(indexName) == true)
                            {
                                indexConfig.RangeKeyAttribute = property.Name;
                                indexConfig.RangeKeyType = GetAttributeType(property.PropertyType);
                                break;
                            }
                        }
                    }
                }

                indexes.Add(indexConfig);
            }

            return indexes;
        }

        /// <summary>
        /// Gets the DynamoDB attribute type for a .NET type.
        /// </summary>
        private static string GetAttributeType(Type propertyType)
        {
            // Handle nullable types
            if (propertyType.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                propertyType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;
            }

            // For simplicity, we'll assume string types for now
            // In a more sophisticated implementation, you might want to handle numbers and binary data
            if (propertyType == typeof(string))
                return "S";
            else if (propertyType == typeof(int) || propertyType == typeof(long) || propertyType == typeof(decimal) ||
                     propertyType == typeof(double) || propertyType == typeof(float))
                return "N";
            else if (propertyType == typeof(byte[]))
                return "B";
            else
                return "S"; // Default to string
        }

        #endregion
        #endregion
    }
}
