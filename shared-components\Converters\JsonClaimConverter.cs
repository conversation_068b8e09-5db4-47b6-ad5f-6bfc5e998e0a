﻿using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    public class JsonClaimConverter : JsonConverter<Claim>
    {
        public override Claim? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string? type = string.Empty;
            string? value = string.Empty;
            string? lastProperty = string.Empty;

            do
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.EndObject:
                        if (type != null && value != null && !string.IsNullOrEmpty(type))
                        {
                            return new Claim(type, value);
                        }
                        return null;
                    case JsonTokenType.PropertyName:
                        lastProperty = reader.GetString();
                        break;
                    case JsonTokenType.String:
                        if (!string.IsNullOrEmpty(lastProperty))
                        {
                            if (lastProperty != null && lastProperty.Equals("Type"))
                            {
                                type = reader.GetString();
                            }
                            else if (lastProperty != null && lastProperty.Equals("Value"))
                            {
                                value = reader.GetString();
                            }
                        }
                        break;
                }
            }
            while (reader.Read());
            return null;
        }

        public override void Write(Utf8JsonWriter writer, Claim value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            writer.WriteString("Type", value.Type);
            writer.WriteString("Value", value.Value);
            writer.WriteEndObject();
        }
    }
}
