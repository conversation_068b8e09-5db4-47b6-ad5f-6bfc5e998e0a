using Amazon.DynamoDBv2;
using Initializer.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using shared.Services;
using shared.Services.Implementation;
using System.Reflection;

namespace Initializer.Services
{
    /// <summary>
    /// Service for initializing and managing NoSQL tables using reflection and generic table managers.
    /// </summary>
    public class TableInitializationService : ITableInitializationService
    {
        private readonly IModelTypeDiscoveryService _modelDiscovery;
        private readonly IAmazonDynamoDB _dynamoClient;
        private readonly ILogger<TableInitializationService> _logger;
        private readonly TableManagementOptions _options;

        public TableInitializationService(
            IModelTypeDiscoveryService modelDiscovery,
            IAmazonDynamoDB dynamoClient,
            ILogger<TableInitializationService> logger,
            IOptions<TableManagementOptions> options)
        {
            _modelDiscovery = modelDiscovery;
            _dynamoClient = dynamoClient;
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// Validates and initializes tables for all discovered model types.
        /// </summary>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeAllTablesAsync()
        {
            _logger.LogInformation("Starting initialization of all discovered tables");

            var modelTypes = _modelDiscovery.DiscoverModelTypes();
            return await InitializeTablesAsync(modelTypes);
        }

        /// <summary>
        /// Validates and initializes tables for specific model types.
        /// </summary>
        /// <param name="modelTypes">Model types to process</param>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeTablesAsync(IEnumerable<Type> modelTypes)
        {
            var modelTypesList = modelTypes.ToList();
            _logger.LogInformation("Starting initialization of {Count} tables", modelTypesList.Count);

            if (!modelTypesList.Any())
            {
                _logger.LogWarning("No model types provided for initialization");
                return true;
            }

            var summary = new InitializationSummary();
            var overallStartTime = DateTime.UtcNow;

            var tasks = modelTypesList.Select(async modelType =>
            {
                var tableName = GetTableName(modelType);
                var startTime = DateTime.UtcNow;
                var result = new TableInitializationResult
                {
                    TableName = tableName,
                    ModelType = modelType.Name
                };

                try
                {
                    _logger.LogDebug("Starting initialization for table '{TableName}' (model: {ModelType})", tableName, modelType.Name);

                    var (success, action, validationErrors) = await InitializeTableWithDetailsAsync(modelType);

                    result.Success = success;
                    result.Action = action;
                    result.ValidationErrors = validationErrors ?? new List<string>();
                    result.Duration = DateTime.UtcNow - startTime;

                    if (success)
                    {
                        _logger.LogDebug("Successfully initialized table '{TableName}' with action: {Action}", tableName, action);
                    }
                    else
                    {
                        result.ErrorMessage = "Initialization returned false";
                        result.Action = TableInitializationAction.Failed;
                    }

                    return result;
                }
                catch (TimeoutException ex)
                {
                    result.Success = false;
                    result.Action = TableInitializationAction.Failed;
                    result.ErrorMessage = $"Timeout: {ex.Message}";
                    result.Duration = DateTime.UtcNow - startTime;
                    _logger.LogError(ex, "Timeout while initializing table '{TableName}' for model type {ModelType}", tableName, modelType.Name);
                    return result;
                }
                catch (InvalidOperationException ex)
                {
                    result.Success = false;
                    result.Action = TableInitializationAction.Failed;
                    result.ErrorMessage = $"Invalid operation: {ex.Message}";
                    result.Duration = DateTime.UtcNow - startTime;
                    _logger.LogError(ex, "Invalid operation while initializing table '{TableName}' for model type {ModelType}", tableName, modelType.Name);
                    return result;
                }
                catch (Exception ex)
                {
                    result.Success = false;
                    result.Action = TableInitializationAction.Failed;
                    result.ErrorMessage = $"Unexpected error: {ex.Message}";
                    result.Duration = DateTime.UtcNow - startTime;
                    _logger.LogError(ex, "Failed to initialize table '{TableName}' for model type {ModelType}", tableName, modelType.Name);
                    return result;
                }
            });

            var results = await Task.WhenAll(tasks);
            summary.Results.AddRange(results);
            summary.TotalDuration = DateTime.UtcNow - overallStartTime;

            // Log comprehensive summary
            summary.LogSummary(_logger);

            return summary.SuccessfulTables == summary.TotalTables;
        }

        /// <summary>
        /// Validates and initializes a table for a specific model type with detailed results.
        /// </summary>
        /// <param name="modelType">Model type to process</param>
        /// <returns>Task representing the operation with success status, action taken, and validation errors</returns>
        private async Task<(bool Success, TableInitializationAction Action, List<string>? ValidationErrors)> InitializeTableWithDetailsAsync(Type modelType)
        {
            _logger.LogInformation("Initializing table for model type {ModelType}", modelType.Name);

            try
            {
                // Create table manager instance using reflection
                var tableManager = CreateTableManager(modelType);
                if (tableManager == null)
                {
                    _logger.LogError("Failed to create table manager for model type {ModelType}", modelType.Name);
                    return (false, TableInitializationAction.Failed, new List<string> { "Failed to create table manager" });
                }

                // Get table name for logging
                var tableName = GetTableName(modelType);
                _logger.LogInformation("Processing table '{TableName}' for model type {ModelType}", tableName, modelType.Name);

                // Check if table exists first
                var tableExists = await TableExistsUsingManager(tableManager);
                _logger.LogDebug("Table '{TableName}' exists: {TableExists}", tableName, tableExists);

                if (!tableExists)
                {
                    // Table doesn't exist, create it
                    _logger.LogInformation("Table '{TableName}' does not exist. Creating new table", tableName);
                    var createSuccess = await CreateTableUsingManager(tableManager);
                    if (!createSuccess)
                    {
                        _logger.LogError("Failed to create table '{TableName}'", tableName);
                        return (false, TableInitializationAction.Failed, new List<string> { "Failed to create table" });
                    }

                    _logger.LogInformation("Table '{TableName}' created successfully", tableName);

                    // Wait for table to become active before validating
                    await WaitForTableToBeActiveUsingManager(tableManager);

                    // Validate the newly created table
                    var newTableValidation = await ValidateTableUsingManager(tableManager);
                    if (!newTableValidation.IsValid)
                    {
                        _logger.LogWarning("Newly created table '{TableName}' failed validation: {Errors}",
                            tableName, string.Join(", ", newTableValidation.ValidationErrors));
                        return (false, TableInitializationAction.Failed, newTableValidation.ValidationErrors);
                    }

                    _logger.LogInformation("Newly created table '{TableName}' validation passed", tableName);
                    return (true, TableInitializationAction.Created, null);
                }

                // Table exists, validate it
                var validationResult = await ValidateTableUsingManager(tableManager);

                if (validationResult.IsValid)
                {
                    _logger.LogInformation("Table '{TableName}' validation passed", tableName);
                    return (true, TableInitializationAction.Validated, null);
                }

                _logger.LogWarning("Table '{TableName}' validation failed: {Errors}",
                    tableName, string.Join(", ", validationResult.ValidationErrors));

                if (!_options.EnableTableRecreation)
                {
                    _logger.LogError("Table recreation is disabled. Table '{TableName}' remains invalid", tableName);
                    return (false, TableInitializationAction.Failed, validationResult.ValidationErrors);
                }

                // Table exists but is invalid, recreate it
                _logger.LogInformation("Recreating table '{TableName}' due to validation failures", tableName);

                // Delete existing table
                _logger.LogInformation("Deleting existing table '{TableName}'", tableName);
                var deleteSuccess = await DeleteTableUsingManager(tableManager);
                if (!deleteSuccess)
                {
                    _logger.LogError("Failed to delete table '{TableName}'", tableName);
                    return (false, TableInitializationAction.Failed, new List<string> { "Failed to delete existing table" });
                }
                _logger.LogInformation("Table '{TableName}' deleted successfully", tableName);

                // Wait for table to be fully deleted
                await WaitForTableToBeDeletedUsingManager(tableManager);

                // Create the table
                _logger.LogInformation("Creating table '{TableName}' after deletion", tableName);
                var createAfterDeleteSuccess = await CreateTableUsingManager(tableManager);
                if (!createAfterDeleteSuccess)
                {
                    _logger.LogError("Failed to create table '{TableName}' after deletion", tableName);
                    return (false, TableInitializationAction.Failed, new List<string> { "Failed to create table after deletion" });
                }

                _logger.LogInformation("Table '{TableName}' recreated successfully", tableName);

                // Wait for table to become active before final validation
                await WaitForTableToBeActiveUsingManager(tableManager);

                // Final validation of recreated table
                var finalValidation = await ValidateTableUsingManager(tableManager);
                if (!finalValidation.IsValid)
                {
                    _logger.LogError("Recreated table '{TableName}' failed final validation: {Errors}",
                        tableName, string.Join(", ", finalValidation.ValidationErrors));
                    return (false, TableInitializationAction.Failed, finalValidation.ValidationErrors);
                }

                _logger.LogInformation("Recreated table '{TableName}' final validation passed", tableName);
                return (true, TableInitializationAction.Recreated, null);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "Timeout while initializing table for model type {ModelType}: {Message}", modelType.Name, ex.Message);
                throw; // Re-throw timeout exceptions so they can be handled specifically by the caller
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Invalid operation while initializing table for model type {ModelType}: {Message}", modelType.Name, ex.Message);
                throw; // Re-throw invalid operation exceptions so they can be handled specifically by the caller
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error initializing table for model type {ModelType}: {Message}", modelType.Name, ex.Message);
                return (false, TableInitializationAction.Failed, new List<string> { ex.Message });
            }
        }

        /// <summary>
        /// Validates and initializes a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to process</param>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeTableAsync(Type modelType)
        {
            var (success, _, _) = await InitializeTableWithDetailsAsync(modelType);
            return success;
        }

        /// <summary>
        /// Validates a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to validate</param>
        /// <returns>Task representing the validation result</returns>
        public async Task<TableValidationResult> ValidateTableAsync(Type modelType)
        {
            _logger.LogDebug("Validating table for model type {ModelType}", modelType.Name);

            try
            {
                var tableManager = CreateTableManager(modelType);
                if (tableManager == null)
                {
                    return new TableValidationResult
                    {
                        IsValid = false,
                        ValidationErrors = new List<string> { $"Failed to create table manager for model type {modelType.Name}" }
                    };
                }

                return await ValidateTableUsingManager(tableManager);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating table for model type {ModelType}", modelType.Name);
                return new TableValidationResult
                {
                    IsValid = false,
                    ValidationErrors = new List<string> { $"Exception during validation: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Creates a table manager instance for the specified model type using reflection.
        /// </summary>
        /// <param name="modelType">Model type</param>
        /// <returns>Table manager instance or null if creation failed</returns>
        private object? CreateTableManager(Type modelType)
        {
            try
            {
                var tableManagerType = typeof(DynamoDBTableManager<>).MakeGenericType(modelType);
                return Activator.CreateInstance(tableManagerType, _dynamoClient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table manager for model type {ModelType}", modelType.Name);
                return null;
            }
        }

        /// <summary>
        /// Gets the table name for a model type using reflection.
        /// </summary>
        /// <param name="modelType">Model type</param>
        /// <returns>Table name</returns>
        private string GetTableName(Type modelType)
        {
            try
            {
                var method = modelType.GetMethod("GetTableName", BindingFlags.Public | BindingFlags.Static);
                if (method != null)
                {
                    return (string)method.Invoke(null, null)!;
                }

                // Fallback to type name
                return modelType.Name;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get table name for model type {ModelType}, using type name", modelType.Name);
                return modelType.Name;
            }
        }

        /// <summary>
        /// Validates a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>Validation result</returns>
        private async Task<TableValidationResult> ValidateTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("ValidateTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("ValidateTableAsync method not found on table manager");
            }

            var task = (Task<TableValidationResult>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Checks if a table exists using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if table exists</returns>
        private async Task<bool> TableExistsUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("TableExistsAsync");
            if (method == null)
            {
                throw new InvalidOperationException("TableExistsAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Deletes a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if deletion was successful</returns>
        private async Task<bool> DeleteTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("DeleteTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("DeleteTableAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Creates a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if creation was successful</returns>
        private async Task<bool> CreateTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("CreateTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("CreateTableAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Waits for a table to become active using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>Task representing the wait operation</returns>
        private async Task WaitForTableToBeActiveUsingManager(object tableManager)
        {
            var tableName = GetTableNameUsingManager(tableManager);
            _logger.LogDebug("Waiting for table '{TableName}' to become active", tableName);

            var timeout = TimeSpan.FromMinutes(_options.WaitTimeoutMinutes);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < timeout)
            {
                try
                {
                    var tableExists = await TableExistsUsingManager(tableManager);
                    if (tableExists)
                    {
                        _logger.LogDebug("Table '{TableName}' is now active", tableName);
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error checking table status for '{TableName}', retrying...", tableName);
                }

                await Task.Delay(TimeSpan.FromSeconds(5));
            }

            throw new TimeoutException($"Table '{tableName}' did not become active within {timeout.TotalMinutes} minutes");
        }

        /// <summary>
        /// Waits for a table to be fully deleted using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>Task representing the wait operation</returns>
        private async Task WaitForTableToBeDeletedUsingManager(object tableManager)
        {
            var tableName = GetTableNameUsingManager(tableManager);
            _logger.LogDebug("Waiting for table '{TableName}' to be fully deleted", tableName);

            var timeout = TimeSpan.FromMinutes(_options.WaitTimeoutMinutes);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < timeout)
            {
                try
                {
                    var tableExists = await TableExistsUsingManager(tableManager);
                    if (!tableExists)
                    {
                        _logger.LogDebug("Table '{TableName}' has been fully deleted", tableName);
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error checking table status for '{TableName}', retrying...", tableName);
                }

                await Task.Delay(TimeSpan.FromSeconds(5));
            }

            throw new TimeoutException($"Table '{tableName}' was not fully deleted within {timeout.TotalMinutes} minutes");
        }

        /// <summary>
        /// Gets the table name using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>Table name</returns>
        private string GetTableNameUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("GetTableName");
            if (method == null)
            {
                throw new InvalidOperationException("GetTableName method not found on table manager");
            }

            return (string)method.Invoke(tableManager, null)!;
        }
    }

    /// <summary>
    /// Represents the result of a table initialization operation.
    /// </summary>
    public class TableInitializationResult
    {
        public string TableName { get; set; } = string.Empty;
        public string ModelType { get; set; } = string.Empty;
        public TableInitializationAction Action { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents the action taken during table initialization.
    /// </summary>
    public enum TableInitializationAction
    {
        Validated,      // Table existed and was valid
        Created,        // Table was created from scratch
        Recreated,      // Table was deleted and recreated
        Failed          // Initialization failed
    }

    /// <summary>
    /// Summary of all table initialization operations.
    /// </summary>
    public class InitializationSummary
    {
        public List<TableInitializationResult> Results { get; set; } = new List<TableInitializationResult>();
        public TimeSpan TotalDuration { get; set; }
        public int TotalTables => Results.Count;
        public int SuccessfulTables => Results.Count(r => r.Success);
        public int FailedTables => Results.Count(r => !r.Success);
        public int ValidatedTables => Results.Count(r => r.Success && r.Action == TableInitializationAction.Validated);
        public int CreatedTables => Results.Count(r => r.Success && r.Action == TableInitializationAction.Created);
        public int RecreatedTables => Results.Count(r => r.Success && r.Action == TableInitializationAction.Recreated);

        public void LogSummary(ILogger logger)
        {
            logger.LogInformation("=== Table Initialization Summary ===");
            logger.LogInformation("Total Duration: {Duration:mm\\:ss}", TotalDuration);
            logger.LogInformation("Total Tables: {Total}, Successful: {Success}, Failed: {Failed}",
                TotalTables, SuccessfulTables, FailedTables);

            if (ValidatedTables > 0)
                logger.LogInformation("Validated (already correct): {Count}", ValidatedTables);
            if (CreatedTables > 0)
                logger.LogInformation("Created (new tables): {Count}", CreatedTables);
            if (RecreatedTables > 0)
                logger.LogInformation("Recreated (fixed invalid): {Count}", RecreatedTables);

            if (FailedTables > 0)
            {
                logger.LogError("Failed Tables:");
                foreach (var failed in Results.Where(r => !r.Success))
                {
                    logger.LogError("  - {TableName} ({ModelType}): {Error}",
                        failed.TableName, failed.ModelType, failed.ErrorMessage ?? "Unknown error");
                }
            }

            logger.LogInformation("=====================================");
        }
    }
}
