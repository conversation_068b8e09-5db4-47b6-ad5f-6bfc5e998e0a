<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>e2118689-7c05-48e1-8443-bfe8eb74cf45</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="3.1.0" />
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="AWSSDK.BedrockAgent" Version="4.0.4" />
    <PackageReference Include="AWSSDK.CognitoIdentityProvider" Version="4.0.1.12" />
    <PackageReference Include="AWSSDK.Core" Version="4.0.0.16" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.2" />
    <PackageReference Include="AWSSDK.IdentityManagement" Version="4.0.2.3" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
    <PackageReference Include="Stateless" Version="5.19.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\shared-components\shared.csproj" />
  </ItemGroup>

</Project>
