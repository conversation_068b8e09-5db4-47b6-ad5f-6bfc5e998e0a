using Amazon.SQS;
using Amazon.SQS.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using shared.Components.MessageBus.Configuration;
using shared.Components.MessageBus.Interfaces;
using shared.Extensions;
using shared.Models.Configuration;
using shared.Models.Enums;
using System.Text.Json;

namespace shared.Components.MessageBus.Implementation
{
    /// <summary>
    /// SQS implementation of the queue manager
    /// </summary>
    public class SQSQueueManager : IMessageBusQueueManager
    {
        private readonly IAmazonSQS _amazonSQS;
        private readonly ILogger<SQSQueueManager> _logger;
        private readonly MicroserviceConfiguration _microserviceConfiguration;
        private readonly MessageBusConfiguration _messageBusConfiguration;
        private readonly Dictionary<MicroserviceType, string> _queueUrlCache = new();
        private readonly object _cachelock = new object();

        public SQSQueueManager(
            IAmazonSQS amazonSQS,
            ILogger<SQSQueueManager> logger,
            IOptions<MicroserviceConfiguration> microserviceConfiguration,
            IOptions<MessageBusConfiguration> messageBusConfiguration)
        {
            _amazonSQS = amazonSQS;
            _logger = logger;
            _microserviceConfiguration = microserviceConfiguration.Value;
            _messageBusConfiguration = messageBusConfiguration.Value;
        }

        /// <summary>
        /// Generate queue name for a microservice
        /// </summary>
        private string MakeQueueName(MicroserviceType microServiceType)
        {
            return $"messagebus-{_microserviceConfiguration.Group}-{microServiceType.DisplayName()}";
        }

        /// <summary>
        /// Generate dead letter queue name for a microservice
        /// </summary>
        private string MakeDeadLetterQueueName(MicroserviceType microServiceType)
        {
            return $"messagebus-{_microserviceConfiguration.Group}-{microServiceType.DisplayName()}-dlq";
        }

        /// <summary>
        /// Check if a queue exists for the specified microservice
        /// </summary>
        public async Task<bool> QueueExistsAsync(MicroserviceType microserviceType)
        {
            try
            {
                var queueName = MakeQueueName(microserviceType);
                var response = await _amazonSQS.GetQueueUrlAsync(queueName);
                return !string.IsNullOrEmpty(response.QueueUrl);
            }
            catch (QueueDoesNotExistException)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if queue exists for microservice {MicroserviceType}", microserviceType);
                return false;
            }
        }

        /// <summary>
        /// Create a queue for the specified microservice
        /// </summary>
        public async Task<bool> CreateQueueAsync(MicroserviceType microserviceType)
        {
            try
            {
                var queueName = MakeQueueName(microserviceType);
                
                _logger.LogInformation("Creating queue {QueueName} for microservice {MicroserviceType}", 
                    queueName, microserviceType);

                // Create main queue
                var createQueueRequest = new CreateQueueRequest
                {
                    QueueName = queueName,
                    Attributes = new Dictionary<string, string>
                    {
                        [QueueAttributeName.VisibilityTimeout] = _messageBusConfiguration.QueueSettings.DefaultVisibilityTimeoutSeconds.ToString(),
                        [QueueAttributeName.MessageRetentionPeriod] = _messageBusConfiguration.QueueSettings.MessageRetentionPeriodSeconds.ToString(),
                        [QueueAttributeName.MaximumMessageSize] = _messageBusConfiguration.QueueSettings.MaxMessageSizeBytes.ToString(),
                        [QueueAttributeName.DelaySeconds] = _messageBusConfiguration.QueueSettings.DelaySeconds.ToString(),
                        [QueueAttributeName.ReceiveMessageWaitTimeSeconds] = _messageBusConfiguration.QueueSettings.ReceiveMessageWaitTimeSeconds.ToString()
                    }
                };

                var createResponse = await _amazonSQS.CreateQueueAsync(createQueueRequest);
                
                if (string.IsNullOrEmpty(createResponse.QueueUrl))
                {
                    _logger.LogError("Failed to create queue {QueueName} - no queue URL returned", queueName);
                    return false;
                }

                // Cache the queue URL
                lock (_cachelock)
                {
                    _queueUrlCache[microserviceType] = createResponse.QueueUrl;
                }

                // Create dead letter queue if enabled
                if (_messageBusConfiguration.QueueSettings.EnableDeadLetterQueue)
                {
                    await CreateDeadLetterQueueAsync(microserviceType, createResponse.QueueUrl);
                }

                _logger.LogInformation("Successfully created queue {QueueName} with URL {QueueUrl}", 
                    queueName, createResponse.QueueUrl);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating queue for microservice {MicroserviceType}", microserviceType);
                return false;
            }
        }

        /// <summary>
        /// Create dead letter queue and configure redrive policy
        /// </summary>
        private async Task CreateDeadLetterQueueAsync(MicroserviceType microserviceType, string mainQueueUrl)
        {
            try
            {
                var dlqName = MakeDeadLetterQueueName(microserviceType);
                
                // Create dead letter queue
                var createDlqRequest = new CreateQueueRequest
                {
                    QueueName = dlqName,
                    Attributes = new Dictionary<string, string>
                    {
                        [QueueAttributeName.MessageRetentionPeriod] = _messageBusConfiguration.QueueSettings.MessageRetentionPeriodSeconds.ToString()
                    }
                };

                var dlqResponse = await _amazonSQS.CreateQueueAsync(createDlqRequest);
                
                if (!string.IsNullOrEmpty(dlqResponse.QueueUrl))
                {
                    // Get DLQ ARN
                    var dlqAttributes = await _amazonSQS.GetQueueAttributesAsync(dlqResponse.QueueUrl, 
                        new List<string> { QueueAttributeName.QueueArn });
                    
                    if (dlqAttributes.Attributes.TryGetValue(QueueAttributeName.QueueArn, out var dlqArn))
                    {
                        // Configure redrive policy on main queue
                        var redrivePolicy = new
                        {
                            deadLetterTargetArn = dlqArn,
                            maxReceiveCount = _messageBusConfiguration.QueueSettings.MaxReceiveCount
                        };

                        var setAttributesRequest = new SetQueueAttributesRequest
                        {
                            QueueUrl = mainQueueUrl,
                            Attributes = new Dictionary<string, string>
                            {
                                [QueueAttributeName.RedrivePolicy] = JsonSerializer.Serialize(redrivePolicy)
                            }
                        };

                        await _amazonSQS.SetQueueAttributesAsync(setAttributesRequest);
                        
                        _logger.LogInformation("Successfully created dead letter queue {DlqName} and configured redrive policy", dlqName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating dead letter queue for microservice {MicroserviceType}", microserviceType);
                // Don't fail the main queue creation if DLQ creation fails
            }
        }

        /// <summary>
        /// Ensure a queue exists for the specified microservice, creating it if necessary
        /// </summary>
        public async Task<bool> EnsureQueueExistsAsync(MicroserviceType microserviceType)
        {
            if (await QueueExistsAsync(microserviceType))
            {
                return true;
            }

            return await CreateQueueAsync(microserviceType);
        }

        /// <summary>
        /// Get queue URL for the specified microservice
        /// </summary>
        public async Task<string> GetQueueUrlAsync(MicroserviceType microserviceType)
        {
            // Check cache first
            lock (_cachelock)
            {
                if (_queueUrlCache.TryGetValue(microserviceType, out var cachedUrl))
                {
                    return cachedUrl;
                }
            }

            try
            {
                var queueName = MakeQueueName(microserviceType);
                var response = await _amazonSQS.GetQueueUrlAsync(queueName);
                
                // Cache the URL
                lock (_cachelock)
                {
                    _queueUrlCache[microserviceType] = response.QueueUrl;
                }

                return response.QueueUrl;
            }
            catch (QueueDoesNotExistException)
            {
                _logger.LogWarning("Queue does not exist for microservice {MicroserviceType}", microserviceType);
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting queue URL for microservice {MicroserviceType}", microserviceType);
                return string.Empty;
            }
        }

        /// <summary>
        /// Delete a queue for the specified microservice
        /// </summary>
        public async Task<bool> DeleteQueueAsync(MicroserviceType microserviceType)
        {
            try
            {
                var queueUrl = await GetQueueUrlAsync(microserviceType);
                if (string.IsNullOrEmpty(queueUrl))
                {
                    return false;
                }

                await _amazonSQS.DeleteQueueAsync(queueUrl);
                
                // Remove from cache
                lock (_cachelock)
                {
                    _queueUrlCache.Remove(microserviceType);
                }

                // Also try to delete dead letter queue
                if (_messageBusConfiguration.QueueSettings.EnableDeadLetterQueue)
                {
                    try
                    {
                        var dlqName = MakeDeadLetterQueueName(microserviceType);
                        var dlqUrl = await _amazonSQS.GetQueueUrlAsync(dlqName);
                        await _amazonSQS.DeleteQueueAsync(dlqUrl.QueueUrl);
                    }
                    catch (QueueDoesNotExistException)
                    {
                        // DLQ doesn't exist, that's fine
                    }
                }

                _logger.LogInformation("Successfully deleted queue for microservice {MicroserviceType}", microserviceType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting queue for microservice {MicroserviceType}", microserviceType);
                return false;
            }
        }

        /// <summary>
        /// Get queue attributes such as message count, visibility timeout, etc.
        /// </summary>
        public async Task<Dictionary<string, string>> GetQueueAttributesAsync(MicroserviceType microserviceType)
        {
            try
            {
                var queueUrl = await GetQueueUrlAsync(microserviceType);
                if (string.IsNullOrEmpty(queueUrl))
                {
                    return new Dictionary<string, string>();
                }

                var attributeNames = new List<string>
                {
                    QueueAttributeName.ApproximateNumberOfMessages,
                    QueueAttributeName.ApproximateNumberOfMessagesNotVisible,
                    QueueAttributeName.VisibilityTimeout,
                    QueueAttributeName.MessageRetentionPeriod,
                    QueueAttributeName.MaximumMessageSize,
                    QueueAttributeName.DelaySeconds,
                    QueueAttributeName.ReceiveMessageWaitTimeSeconds,
                    QueueAttributeName.RedrivePolicy
                };

                var response = await _amazonSQS.GetQueueAttributesAsync(queueUrl, attributeNames);
                return response.Attributes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting queue attributes for microservice {MicroserviceType}", microserviceType);
                return new Dictionary<string, string>();
            }
        }
    }
}
