using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Main interface for the FlowBuilder component that provides flow management and execution capabilities.
    /// This interface allows for dependency injection and supports multiple FlowBuilder instances.
    /// </summary>
    public interface IFlowBuilder
    {
        /// <summary>
        /// Gets the unique identifier for this FlowBuilder instance.
        /// </summary>
        string InstanceId { get; }

        /// <summary>
        /// Gets the name/type of this FlowBuilder instance (e.g., "LLMBehaviorFlow", "MultiAgentFlow").
        /// </summary>
        string InstanceName { get; }

        /// <summary>
        /// Validates a flow definition to ensure all nodes, edges, and tasks are properly configured.
        /// </summary>
        /// <param name="flow">The flow to validate</param>
        /// <returns>Validation result with any errors or warnings</returns>
        Task<FlowValidationResult> ValidateFlowAsync(Flow flow);

        /// <summary>
        /// Executes a flow asynchronously and returns the execution context for monitoring.
        /// </summary>
        /// <param name="flowId">The ID of the flow to execute</param>
        /// <param name="executionContext">Initial execution context with input values</param>
        /// <param name="cancellationToken">Cancellation token for stopping execution</param>
        /// <returns>Flow execution result</returns>
        Task<FlowExecutionResult> ExecuteFlowAsync(string flowId, FlowExecutionContext executionContext, CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts a flow execution in the background using the event bus system.
        /// </summary>
        /// <param name="flowId">The ID of the flow to execute</param>
        /// <param name="executionContext">Initial execution context with input values</param>
        /// <returns>Execution session ID for tracking</returns>
        Task<string> StartFlowExecutionAsync(string flowId, FlowExecutionContext executionContext);

        /// <summary>
        /// Gets the current execution status of a running flow.
        /// </summary>
        /// <param name="executionSessionId">The execution session ID</param>
        /// <returns>Current execution status</returns>
        Task<FlowExecutionStatus> GetExecutionStatusAsync(string executionSessionId);

        /// <summary>
        /// Cancels a running flow execution.
        /// </summary>
        /// <param name="executionSessionId">The execution session ID to cancel</param>
        /// <returns>True if cancellation was successful</returns>
        Task<bool> CancelExecutionAsync(string executionSessionId);

        /// <summary>
        /// Registers a custom node type for this FlowBuilder instance.
        /// </summary>
        /// <param name="nodeType">The node type identifier</param>
        /// <param name="nodeFactory">Factory function to create instances of this node type</param>
        void RegisterCustomNode(string nodeType, Func<FlowNodeDefinition, IFlowNode> nodeFactory);

        /// <summary>
        /// Registers a custom task type for this FlowBuilder instance.
        /// </summary>
        /// <param name="taskType">The task type identifier</param>
        /// <param name="taskFactory">Factory function to create instances of this task type</param>
        void RegisterCustomTask(string taskType, Func<FlowTaskDefinition, IFlowTask> taskFactory);

        /// <summary>
        /// Gets all registered node types for this FlowBuilder instance.
        /// </summary>
        /// <returns>Dictionary of node type names and their factory functions</returns>
        IReadOnlyDictionary<string, Func<FlowNodeDefinition, IFlowNode>> GetRegisteredNodeTypes();

        /// <summary>
        /// Gets all registered task types for this FlowBuilder instance.
        /// </summary>
        /// <returns>Dictionary of task type names and their factory functions</returns>
        IReadOnlyDictionary<string, Func<FlowTaskDefinition, IFlowTask>> GetRegisteredTaskTypes();
    }
}
