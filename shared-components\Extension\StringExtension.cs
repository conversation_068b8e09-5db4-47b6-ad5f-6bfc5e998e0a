﻿using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Text;
using System.Text.RegularExpressions;

namespace shared.Extension
{
    public static class StringExtensions
    {
        /*public static string FormatFromDictionary(this string formatString, Dictionary<string, string> valueDict, string prefix = "")
        {
            if(valueDict.Count == 0) return formatString;
            int i = 0;
            StringBuilder newFormatString = new StringBuilder(formatString);
            Dictionary<int, string> intToKey = new Dictionary<int, string>();
            foreach (var tuple in valueDict)
            {
                string replacement = "{" + i.ToString() + "}";
                newFormatString = newFormatString.Replace("{" + prefix + tuple.Key + "}", "{" + i.ToString() + "}");
                intToKey.Add(i, tuple.Value);
                i++;
            }
            string res = String.Format(newFormatString.ToString(), valueDict.OrderBy(x => x.Key).Select(x => x.Value).ToArray());
            return res;
        }*/

        public static int IndexOf(this StringBuilder sb, string value, int startIndex, bool ignoreCase)
        {
            int len = value.Length;
            int max = (sb.Length - len) + 1;
            var v1 = (ignoreCase)
                ? value.ToLower() : value;
            var func1 = (ignoreCase)
                ? new Func<char, char, bool>((x, y) => char.ToLower(x) == y)
                : new Func<char, char, bool>((x, y) => x == y);
            for (int i1 = startIndex; i1 < max; ++i1)
                if (func1(sb[i1], v1[0]))
                {
                    int i2 = 1;
                    while ((i2 < len) && func1(sb[i1 + i2], v1[i2]))
                        ++i2;
                    if (i2 == len)
                        return i1;
                }
            return -1;
        }

        public static string FormatFromDictionary(this string formatString, Dictionary<string, string> valueDict, string prefix = "", bool cleanMissing = true)
        {
            if (valueDict.Count == 0) return formatString;
            StringBuilder newFormatString = new StringBuilder(formatString);
            foreach (var tuple in valueDict)
            {
                string current_key = "{{" + prefix + tuple.Key + "}}";

                int index = newFormatString.IndexOf(current_key, 0, false);
                if (index == -1)
                {
                    continue;
                }
                newFormatString = newFormatString.Replace(current_key, tuple.Value);
            }
            string result = newFormatString.ToString();
            if (cleanMissing)
            {
                result = Regex.Replace(result, "{{" + prefix + "\\w+}}", "");
            }
            return result;
        }

        public static string FormatFromDictionary(this string formatString, Dictionary<string, Func<string>> valueDict, string prefix = "", bool cleanMissing = true)
        {
            if (valueDict.Count == 0) return formatString;

            List<string> matches = new List<string>();
            var new_dictionary = new Dictionary<string, string>();

            var match = Regex.Match(formatString, "{{" + prefix + "(\\w+)}}");
            if (match.Success && match.Groups.Count > 1 && match.Groups[1].Captures.Count > 0)
            {
                match.Groups[1].Captures.ToList().ForEach(i => {
                    if (valueDict.ContainsKey(i.Value)) {
                        new_dictionary.Add(i.Value, valueDict[i.Value]());
                    }
                });
            }
            else
            {
                return formatString;
            }

            return formatString.FormatFromDictionary(new_dictionary, prefix, cleanMissing);
        }

    }



}
