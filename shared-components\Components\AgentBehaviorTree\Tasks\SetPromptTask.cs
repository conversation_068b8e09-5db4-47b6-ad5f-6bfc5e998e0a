﻿using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;
using shared.Types;

namespace shared.Components.AgentBehaviorTree.Tasks
{
    public class SetPromptTask : Node
    {
        public PromptEntry PromptEntry { get; set; } = new PromptEntry();

        public SetPromptTask(string nodeId, List<Node>? children, ConditionExpression? conditionExpression, PromptEntry promptEntry) : base(nodeId, children, conditionExpression)
        {
            PromptEntry = promptEntry;
        }

        protected override Task<NodeState> EvaluateImpl(TreeState treeState)
        {
            ((AgentTreeState)treeState).Result.Prompt.PutPrompt(PromptEntry);
            return Task.FromResult(NodeState.SUCCESS);
        }
    }
}
