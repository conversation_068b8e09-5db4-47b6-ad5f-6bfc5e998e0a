﻿using Initializer.Configuration;
using Initializer.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Initializer
{
    /// <summary>
    /// Console application for initializing and managing NoSQL tables.
    /// </summary>
    public class Program
    {
        public static async Task<int> Main(string[] args)
        {
            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                    .AddEnvironmentVariables()
                    .AddCommandLine(args)
                    .Build();

                // Build host
                var host = Host.CreateDefaultBuilder(args)
                    .ConfigureServices((context, services) =>
                    {
                        services.AddInitializerServices(configuration);
                    })
                    .ConfigureLogging(logging =>
                    {
                        logging.ClearProviders();
                        logging.AddConsole();
                        logging.AddConfiguration(configuration.GetSection("Logging"));
                    })
                    .Build();

                // Run the application
                using var scope = host.Services.CreateScope();
                var app = new InitializerApplication(scope.ServiceProvider);
                await app.RunAsync(args);

                Console.WriteLine("Press any key to exit.");
                Console.ReadLine();
                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fatal error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return 1;
            }
        }
    }

    /// <summary>
    /// Main application logic for the Initializer.
    /// </summary>
    public class InitializerApplication
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<InitializerApplication> _logger;
        private readonly ITableInitializationService _tableInitializationService;
        private readonly IModelTypeDiscoveryService _modelDiscoveryService;
        private readonly TableManagementOptions _options;

        public InitializerApplication(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetRequiredService<ILogger<InitializerApplication>>();
            _tableInitializationService = serviceProvider.GetRequiredService<ITableInitializationService>();
            _modelDiscoveryService = serviceProvider.GetRequiredService<IModelTypeDiscoveryService>();
            _options = serviceProvider.GetRequiredService<IOptions<TableManagementOptions>>().Value;
        }

        /// <summary>
        /// Runs the application with the provided command line arguments.
        /// </summary>
        /// <param name="args">Command line arguments</param>
        /// <returns>Exit code</returns>
        public async Task<int> RunAsync(string[] args)
        {
            _logger.LogInformation("Starting Initializer application");
            _logger.LogInformation("Table recreation enabled: {EnableRecreation}", _options.EnableTableRecreation);
            _logger.LogInformation("Validate tables on startup: {ValidateOnStartup}", _options.ValidateTablesOnStartup);

            try
            {
                if (args.Length == 0)
                {
                    // Default behavior: initialize all tables
                    return await InitializeAllTablesAsync();
                }

                var command = args[0].ToLowerInvariant();
                switch (command)
                {
                    case "init":
                    case "initialize":
                        return await InitializeAllTablesAsync();

                    case "validate":
                        return await ValidateAllTablesAsync();

                    case "discover":
                        return await DiscoverModelsAsync();

                    case "help":
                    case "--help":
                    case "-h":
                        ShowHelp();
                        return 0;

                    default:
                        _logger.LogError("Unknown command: {Command}", command);
                        ShowHelp();
                        return 1;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Application error");
                return 1;
            }
        }

        /// <summary>
        /// Initializes all discovered tables.
        /// </summary>
        /// <returns>Exit code</returns>
        private async Task<int> InitializeAllTablesAsync()
        {
            _logger.LogInformation("Initializing all tables");

            var success = await _tableInitializationService.InitializeAllTablesAsync();

            if (success)
            {
                _logger.LogInformation("All tables initialized successfully");
                return 0;
            }
            else
            {
                _logger.LogError("Some tables failed to initialize");
                return 1;
            }
        }

        /// <summary>
        /// Validates all discovered tables without making changes.
        /// </summary>
        /// <returns>Exit code</returns>
        private async Task<int> ValidateAllTablesAsync()
        {
            _logger.LogInformation("Validating all tables");

            var modelTypes = _modelDiscoveryService.DiscoverModelTypes();
            var allValid = true;

            foreach (var modelType in modelTypes)
            {
                try
                {
                    var result = await _tableInitializationService.ValidateTableAsync(modelType);

                    if (result.IsValid)
                    {
                        _logger.LogInformation("Table for {ModelType} is valid", modelType.Name);
                    }
                    else
                    {
                        _logger.LogWarning("Table for {ModelType} is invalid: {Errors}",
                            modelType.Name, string.Join(", ", result.ValidationErrors));
                        allValid = false;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error validating table for {ModelType}", modelType.Name);
                    allValid = false;
                }
            }

            if (allValid)
            {
                _logger.LogInformation("All tables are valid");
                return 0;
            }
            else
            {
                _logger.LogWarning("Some tables are invalid");
                return 1;
            }
        }

        /// <summary>
        /// Discovers and lists all model types.
        /// </summary>
        /// <returns>Exit code</returns>
        private async Task<int> DiscoverModelsAsync()
        {
            _logger.LogInformation("Discovering model types");

            var modelTypes = _modelDiscoveryService.DiscoverModelTypes().ToList();

            _logger.LogInformation("Found {Count} model types:", modelTypes.Count);

            foreach (var modelType in modelTypes.OrderBy(t => t.Name))
            {
                _logger.LogInformation("  - {ModelType} (Assembly: {Assembly})",
                    modelType.Name, modelType.Assembly.GetName().Name);
            }

            return 0;
        }

        /// <summary>
        /// Shows help information.
        /// </summary>
        private void ShowHelp()
        {
            Console.WriteLine("Initializer - NoSQL Table Management Tool");
            Console.WriteLine();
            Console.WriteLine("Usage: Initializer [command]");
            Console.WriteLine();
            Console.WriteLine("Commands:");
            Console.WriteLine("  init, initialize  Initialize all tables (validate, delete if invalid, create)");
            Console.WriteLine("  validate          Validate all tables without making changes");
            Console.WriteLine("  discover          Discover and list all model types");
            Console.WriteLine("  help              Show this help message");
            Console.WriteLine();
            Console.WriteLine("If no command is specified, 'initialize' is used by default.");
            Console.WriteLine();
            Console.WriteLine("Configuration:");
            Console.WriteLine("  Use appsettings.json to configure AWS settings and table management options.");
            Console.WriteLine("  Set ASPNETCORE_ENVIRONMENT to 'Development' to use appsettings.Development.json.");
        }
    }
}
