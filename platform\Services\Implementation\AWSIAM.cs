﻿using Amazon.IdentityManagement;
using Amazon.IdentityManagement.Model;
using Microsoft.Extensions.Caching.Memory;
using platform.Models.Service;
using platform.Services;
using shared.Extension;
using System.Text.RegularExpressions;

namespace platform.Components.AWS
{
    public class AWSIAM : IIAM
    {

        private readonly IAmazonIdentityManagementService identityManagementService;
        private readonly IMemoryCache memoryCache;
        private string accountNumber = string.Empty;

        public AWSIAM(IMemoryCache memoryCache) {
            this.identityManagementService = new AmazonIdentityManagementServiceClient();
            this.memoryCache = memoryCache;
        }

        public async Task<string> GetProviderAccountNumber() {
            return await memoryCache.GetOrCreateAsync<string>(_GetAccountNumber) ?? string.Empty;
        }

        private async Task<string> _GetAccountNumber(ICacheEntry entry)
        {

            var currentUser = await identityManagementService.GetUserAsync();

            var match = Regex.Match(currentUser.User.Arn, @"^[^:]*:[^:]*:[^:]*:[^:]*:([^:]+)");
            if (match != null)
            {
                return match.Groups[1].Value;
            }
            return string.Empty;
        }

        public async Task<IAMPolicyResponse> GetPolicy(string policyName)
        {

            if (string.IsNullOrEmpty(accountNumber)) {
                accountNumber = await GetProviderAccountNumber();
            }

            string arn = $"arn:aws:iam::{accountNumber}:policy/{policyName}";

            IAMPolicyResponse response = new IAMPolicyResponse();

            try
            {
                GetPolicyResponse getResponse = await identityManagementService.GetPolicyAsync(new GetPolicyRequest { PolicyArn = arn });
                response.Success = true;
                response.PolicyId = arn;

            } catch (Exception ex) {
                
            }
            return response;
        }

        public async Task<IAMPolicyResponse> CreatePolicy(string policyName, string path, bool errorIfAlreadyExists = false)
        {
            CreatePolicyRequest policyRequest = new CreatePolicyRequest
            {
                PolicyName = policyName,
                PolicyDocument = System.IO.File.ReadAllText(path)
            };

            IAMPolicyResponse response = new IAMPolicyResponse();
            try
            {
                var createResponse = await identityManagementService.CreatePolicyAsync(policyRequest);
                response.Success = true;
                response.PolicyId = createResponse.Policy.Arn;

            }catch(EntityAlreadyExistsException ex)
            {
                if (!errorIfAlreadyExists)
                {

                    return response;
                }

            }
            
            return response;

        }
    }
}
