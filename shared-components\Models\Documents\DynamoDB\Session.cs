using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Models.Document;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(Session))]
    public class Session : DynamoDBModel
    {

        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string SessionId { get; set; } = string.Empty;
        public string TreeId { get; set; } = string.Empty;
        public AgentTreeState TreeState { get; set; } = new AgentTreeState();
        public object? ProviderData { get; set; }

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(Session.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(Session.SessionId);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(Session);
        }
    }
}
