using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Base interface for all flow nodes. Nodes define how the flow progresses and can include
    /// control structures like sequences, loops, parallel execution, etc.
    /// </summary>
    public interface IFlowNode
    {
        /// <summary>
        /// Gets the unique identifier for this node instance.
        /// </summary>
        string NodeId { get; }

        /// <summary>
        /// Gets the type of this node (e.g., "Sequence", "Parallel", "Loop", etc.).
        /// </summary>
        string NodeType { get; }

        /// <summary>
        /// Gets the display name for this node.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the node definition that contains configuration data.
        /// </summary>
        FlowNodeDefinition Definition { get; }

        /// <summary>
        /// Gets the input handles for this node (where connections can be made from other nodes).
        /// </summary>
        IReadOnlyList<NodeHandle> InputHandles { get; }

        /// <summary>
        /// Gets the output handles for this node (where connections can be made to other nodes).
        /// </summary>
        IReadOnlyList<NodeHandle> OutputHandles { get; }

        /// <summary>
        /// Initializes the node with its definition and validates configuration.
        /// </summary>
        /// <param name="definition">The node definition containing configuration</param>
        /// <returns>True if initialization was successful</returns>
        Task<bool> InitializeAsync(FlowNodeDefinition definition);

        /// <summary>
        /// Validates the node configuration and connections.
        /// </summary>
        /// <param name="context">Validation context with flow information</param>
        /// <returns>Validation result</returns>
        Task<NodeValidationResult> ValidateAsync(FlowValidationContext context);

        /// <summary>
        /// Executes the node logic asynchronously.
        /// </summary>
        /// <param name="context">Execution context with state and field access</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Execution result indicating next steps</returns>
        Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Evaluates node conditions to determine if the node should execute.
        /// </summary>
        /// <param name="context">Execution context with field values</param>
        /// <returns>True if conditions are met and node should execute</returns>
        Task<bool> EvaluateConditionsAsync(FlowExecutionContext context);

        /// <summary>
        /// Gets the next nodes to execute based on the execution result.
        /// </summary>
        /// <param name="executionResult">The result from executing this node</param>
        /// <param name="context">Current execution context</param>
        /// <returns>List of next node IDs and their corresponding output handles</returns>
        Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context);

        /// <summary>
        /// Called when the node execution is completed (successfully or with error).
        /// </summary>
        /// <param name="result">The execution result</param>
        /// <param name="context">Execution context</param>
        Task OnExecutionCompletedAsync(NodeExecutionResult result, FlowExecutionContext context);

        /// <summary>
        /// Called when the node execution is cancelled.
        /// </summary>
        /// <param name="context">Execution context</param>
        Task OnExecutionCancelledAsync(FlowExecutionContext context);
    }

    /// <summary>
    /// Information about the next node to execute.
    /// </summary>
    public class NextNodeInfo
    {
        public string NodeId { get; set; } = string.Empty;
        public string OutputHandle { get; set; } = string.Empty;
        public string InputHandle { get; set; } = string.Empty;
        public Dictionary<string, object> TransferData { get; set; } = new();
    }

    /// <summary>
    /// Represents a connection handle on a node.
    /// </summary>
    public class NodeHandle
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public NodeHandleType Type { get; set; }
        public string Position { get; set; } = string.Empty; // e.g., "left", "right", "top", "bottom"
    }

    /// <summary>
    /// Types of node handles.
    /// </summary>
    public enum NodeHandleType
    {
        Input,
        Output
    }
}
