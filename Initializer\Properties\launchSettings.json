{"profiles": {"Initializer (Native)": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "commandLineArgs": "init"}, "Initializer (Native - Validate Only)": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "commandLineArgs": "validate"}, "Initializer (Native - Discover Models)": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "commandLineArgs": "discover"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}