﻿using shared.Extensions;
using System.ComponentModel;

namespace shared.Helpers
{
    public static class CUI
    {
        public enum Resource
        {
            [Description("AG")]
            Agent,
            [Description("KB")]
            KnowledgebBase,
            [Description("AK")]
            AgentKnowledgebaseLink,
            [Description("AT")]
            AgentTag,
            [Description("AA")]
            AgentAlias,
            [Description("KF")]
            KnowledgebaseFile,
            [Description("SS")]
            Session,
            [Description("BT")]
            BehaviorTree
        }

        public static string Generate(Resource resource)
        {
            return resource.GetDescription() + ":" + Convert.ToBase64String(Guid.NewGuid().ToByteArray())
            .Substring(0, 22)
            .Replace("/", "_")
            .Replace("+", "-");
        }
    }
}
