﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Encodings.Web;
using shared.Services;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using shared.Models.Configuration;
using Amazon.Auth.AccessControlPolicy;

namespace shared.Authentication
{
    public class SecretsJwtBearerHandler : AuthenticationHandler<SecretsJwtBearerOptions>
    {
        private readonly ISecretsService secretsService;
        private readonly IOptions<MicroserviceConfiguration> microserviceConfiguration;

        public SecretsJwtBearerHandler(
            IOptionsMonitor<SecretsJwtBearerOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISecretsService secretsService,
            IOptions<MicroserviceConfiguration> microserviceConfiguration)
            : base(options, logger, encoder)
        {
            this.secretsService = secretsService;
            this.microserviceConfiguration = microserviceConfiguration;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var token = Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(await secretsService.Get(microserviceConfiguration.Value.Environment, Options.SecretsKeyName)));
            Options.TokenValidationParameters.IssuerSigningKey = key;
            try
            {
                var claimsPrincipal = tokenHandler.ValidateToken(token, Options.TokenValidationParameters, out SecurityToken validatedToken);
                var ticket = new AuthenticationTicket(claimsPrincipal, Scheme.Name);
                return AuthenticateResult.Success(ticket);
            }
            catch (Exception ex)
            {
                return AuthenticateResult.Fail(ex);
            }
        }

    }
}
