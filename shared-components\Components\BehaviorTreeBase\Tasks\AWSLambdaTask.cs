﻿using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Components.BehaviorTreeBase.Models;

namespace shared.Components.BehaviorTreeBase.Tasks
{
    public class AWSLambdaTask : Node
    {
        public string FunctionId { get; set; } = string.Empty;

        public AWSLambdaTask(string nodeId, List<Node>? children, ConditionExpression? conditionExpression, string functionId) : base(nodeId, children, conditionExpression)
        {
            FunctionId = functionId;
        }

        protected override Task<NodeState> EvaluateImpl(TreeState treeState)
        {
            //ToDo
            throw new NotImplementedException();
        }
    }
}
