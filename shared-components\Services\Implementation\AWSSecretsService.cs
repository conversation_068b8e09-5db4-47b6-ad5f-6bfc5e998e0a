﻿using Amazon.SecretsManager;
using Amazon.SecretsManager.Extensions.Caching;
using shared.Models.Configuration;

namespace shared.Services.Implementation
{
    public class AWSSecretsService : ISecretsService
    {
        private readonly IAmazonSecretsManager secretsManager;
        private readonly SecretsManagerCache cache;

        public AWSSecretsService(ILogger<AWSSecretsService> logger, IAmazonSecretsManager secretsManager)
        {
            this.secretsManager = secretsManager;
            cache = new SecretsManagerCache(secretsManager, new SecretCacheConfiguration());
        }

        public async Task<string> Get(string secretName)
        {
            return await cache.GetSecretString(secretName);
        }

        public async Task<string> Get(string environment, string secretName)
        {
            return await Get(environment + "/" + secretName);
        }
    }
}
