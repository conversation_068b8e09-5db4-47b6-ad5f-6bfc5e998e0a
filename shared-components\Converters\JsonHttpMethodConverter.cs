﻿using System.Text.Json;
using System;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    public class JsonHttpMethodConverter: JsonConverter<HttpMethod>
    {
        public override HttpMethod? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return new HttpMethod(reader.GetString() ?? "GET");
        }

        public override void Write(Utf8JsonWriter writer, HttpMethod value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.Method);
        }
    }
}
