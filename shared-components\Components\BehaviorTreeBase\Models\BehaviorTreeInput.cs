﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Converters;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Models
{
    public class BehaviorTreeInput
    {
        public static readonly Dictionary<BehaviorTreeInputType, Type> BehaviorTreeInputTypeToType = new Dictionary<BehaviorTreeInputType, Type> {
            { BehaviorTreeInputType.INTEGER, typeof(int) },
            { BehaviorTreeInputType.BOOLEAN, typeof(bool) },
            { BehaviorTreeInputType.DOUBLE, typeof(double) },
            { BehaviorTreeInputType.STRING, typeof(string) },
            { BehaviorTreeInputType.JSON, typeof(JsonObject) }
        };

        public static readonly Dictionary<BehaviorTreeInputType, JsonValueKind> BehaviorTreeInputTypeToJsonElementType = new Dictionary<BehaviorTreeInputType, JsonValueKind> {
            { BehaviorTreeInputType.INTEGER, JsonValueKind.Number },
            { BehaviorTreeInputType.BOOLEAN, JsonValueKind.False | JsonValueKind.True },
            { BehaviorTreeInputType.DOUBLE, JsonValueKind.Number },
            { BehaviorTreeInputType.STRING, JsonValueKind.String },
            { BehaviorTreeInputType.JSON, JsonValueKind.Object }
        };

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<BehaviorTreeInputType>))]
        [JsonConverter(typeof(JsonEnumStringConverter<BehaviorTreeInputType>))]
        public BehaviorTreeInputType InputType { get; set; }
        public bool Required { get; set; }
    }
}
