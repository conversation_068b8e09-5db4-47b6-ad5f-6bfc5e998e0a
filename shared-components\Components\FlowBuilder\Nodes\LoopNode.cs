using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.Interfaces;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Loop Node - Repeats execution based on conditions.
    /// Has 1 input (input handle on left) and 2 outputs (loop handle at 33%, exit handle at 66%).
    /// </summary>
    public class LoopNode : BaseFlowNode
    {
        private LoopNodeDefinition? _nodeConfig;

        public LoopNode()
        {
            NodeType = "Loop";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Loop output handle (continue loop)
            _outputHandles.Add(new NodeHandle
            {
                Id = "loop",
                Name = "Loop",
                Type = NodeHandleType.Output,
                Position = "right-33" // At 33% position
            });

            // Exit output handle (break loop)
            _outputHandles.Add(new NodeHandle
            {
                Id = "exit",
                Name = "Exit",
                Type = NodeHandleType.Output,
                Position = "right-66" // At 66% position
            });

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new LoopNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata
                };

                // Extract loop condition from configuration
                if (definition.Configuration.ContainsKey("loopCondition"))
                {
                    var conditionData = definition.Configuration["loopCondition"];
                    if (conditionData is FlowRuleGroup ruleGroup)
                    {
                        _nodeConfig.LoopCondition = ruleGroup;
                    }
                }

                return await Task.FromResult(true);
            }
            catch (Exception)
            {
                return await Task.FromResult(false);
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate loop condition
            if (_nodeConfig.LoopCondition == null)
            {
                result.Errors.Add("Loop condition is required");
            }
            else
            {
                // Validate that the loop condition references valid fields
                ValidateRuleGroup(_nodeConfig.LoopCondition, context, result);
            }

            // Validate connections
            var loopConnections = context.AllEdges.Where(e => e.Source == NodeId && e.SourceHandle == "loop").Count();
            var exitConnections = context.AllEdges.Where(e => e.Source == NodeId && e.SourceHandle == "exit").Count();

            if (loopConnections == 0)
            {
                result.Warnings.Add("Loop node has no loop connections - loop will not iterate");
            }

            if (exitConnections == 0)
            {
                result.Warnings.Add("Loop node has no exit connections - consider adding exit path");
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // Initialize loop state if not present
                var loopStateKey = $"loop_{NodeId}_state";
                if (!context.ExecutionData.ContainsKey(loopStateKey))
                {
                    context.ExecutionData[loopStateKey] = new Dictionary<string, object>
                    {
                        ["iterationCount"] = 0,
                        ["maxIterations"] = 1000, // Safety limit
                        ["startTime"] = DateTime.UtcNow
                    };
                }

                var loopState = (Dictionary<string, object>)context.ExecutionData[loopStateKey];
                var iterationCount = (int)loopState["iterationCount"];
                var maxIterations = (int)loopState["maxIterations"];

                // Safety check for infinite loops
                if (iterationCount >= maxIterations)
                {
                    return CreateFailureResult($"Loop exceeded maximum iterations ({maxIterations})");
                }

                // Evaluate loop condition
                bool shouldContinueLoop = false;
                if (_nodeConfig.LoopCondition != null)
                {
                    shouldContinueLoop = await EvaluateRuleGroupAsync(_nodeConfig.LoopCondition, context);
                }

                // Update loop state
                loopState["iterationCount"] = iterationCount + 1;
                loopState["lastEvaluationTime"] = DateTime.UtcNow;

                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "loop",
                    ["shouldContinueLoop"] = shouldContinueLoop,
                    ["iterationCount"] = iterationCount + 1,
                    ["maxIterations"] = maxIterations
                };

                // Store loop execution information
                context.ExecutionData["loopNodeId"] = NodeId;
                context.ExecutionData["loopShouldContinue"] = shouldContinueLoop;
                context.ExecutionData["loopIterationCount"] = iterationCount + 1;

                // Set variable fields for loop state
                context.SetFieldValue($"loop.{NodeId}.iteration", iterationCount + 1);
                context.SetFieldValue($"loop.{NodeId}.shouldContinue", shouldContinueLoop);

                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata about loop execution
                result.Metadata["executionMode"] = "loop";
                result.Metadata["shouldContinueLoop"] = shouldContinueLoop;
                result.Metadata["iterationCount"] = iterationCount + 1;

                // Determine next nodes based on loop condition
                if (shouldContinueLoop)
                {
                    result.NextNodeIds = new List<string> { "loop" }; // Continue loop
                }
                else
                {
                    result.NextNodeIds = new List<string> { "exit" }; // Exit loop
                }

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Loop node: {ex.Message}", ex);
            }
        }

        public override async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            if (executionResult.Status == NodeExecutionStatus.Completed)
            {
                var shouldContinueLoop = executionResult.Metadata.GetValueOrDefault("shouldContinueLoop", false);
                var iterationCount = executionResult.Metadata.GetValueOrDefault("iterationCount", 0);

                if ((bool)shouldContinueLoop)
                {
                    // Continue loop - find nodes connected to loop handle
                    nextNodes.Add(new NextNodeInfo
                    {
                        NodeId = "loop", // This will be resolved by the flow engine
                        OutputHandle = "loop",
                        InputHandle = "input",
                        TransferData = new Dictionary<string, object>
                        {
                            ["isLoopIteration"] = true,
                            ["iterationCount"] = iterationCount,
                            ["loopNodeId"] = NodeId
                        }
                    });
                }
                else
                {
                    // Exit loop - find nodes connected to exit handle
                    nextNodes.Add(new NextNodeInfo
                    {
                        NodeId = "exit", // This will be resolved by the flow engine
                        OutputHandle = "exit",
                        InputHandle = "input",
                        TransferData = new Dictionary<string, object>
                        {
                            ["isLoopExit"] = true,
                            ["finalIterationCount"] = iterationCount,
                            ["loopNodeId"] = NodeId
                        }
                    });
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// Validates a rule group and its nested rules.
        /// </summary>
        private void ValidateRuleGroup(FlowRuleGroup ruleGroup, FlowValidationContext context, NodeValidationResult result)
        {
            foreach (var rule in ruleGroup.Rules)
            {
                if (!context.AvailableFields.ContainsKey(rule.Field))
                {
                    result.Warnings.Add($"Loop condition references unknown field: {rule.Field}");
                }
            }

            foreach (var nestedGroup in ruleGroup.Groups)
            {
                ValidateRuleGroup(nestedGroup, context, result);
            }
        }

        /// <summary>
        /// Gets the configured loop condition.
        /// </summary>
        public FlowRuleGroup? GetLoopCondition()
        {
            return _nodeConfig?.LoopCondition;
        }
    }
}
