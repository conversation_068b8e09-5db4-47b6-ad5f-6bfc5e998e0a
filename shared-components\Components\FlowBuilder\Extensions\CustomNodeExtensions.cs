using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using System.Reflection;

namespace shared.Components.FlowBuilder.Extensions
{
    /// <summary>
    /// Extension system for registering custom nodes and tasks in FlowBuilder.
    /// Allows projects to define their own node and task types with proper validation.
    /// </summary>
    public static class CustomNodeExtensions
    {
        /// <summary>
        /// Register a custom node type with automatic discovery of metadata.
        /// </summary>
        /// <typeparam name="TNode">The node type to register</typeparam>
        /// <param name="flowBuilder">The FlowBuilder instance</param>
        /// <param name="nodeType">Optional custom node type name (defaults to class name)</param>
        /// <returns>The FlowBuilder instance for chaining</returns>
        public static IFlowBuilder RegisterCustomNode<TNode>(this IFlowBuilder flowBuilder, string? nodeType = null)
            where TNode : class, IFlowNode, new()
        {
            var actualNodeType = nodeType ?? typeof(TNode).Name;
            
            // Validate that the node type implements IFlowNode properly
            ValidateNodeType<TNode>();
            
            flowBuilder.RegisterCustomNode(actualNodeType, def => 
            {
                var node = new TNode();
                return node;
            });
            
            return flowBuilder;
        }

        /// <summary>
        /// Register a custom task type with automatic discovery of metadata.
        /// </summary>
        /// <typeparam name="TTask">The task type to register</typeparam>
        /// <param name="flowBuilder">The FlowBuilder instance</param>
        /// <param name="taskType">Optional custom task type name (defaults to class name)</param>
        /// <returns>The FlowBuilder instance for chaining</returns>
        public static IFlowBuilder RegisterCustomTask<TTask>(this IFlowBuilder flowBuilder, string? taskType = null)
            where TTask : class, IFlowTask, new()
        {
            var actualTaskType = taskType ?? typeof(TTask).Name;
            
            // Validate that the task type implements IFlowTask properly
            ValidateTaskType<TTask>();
            
            flowBuilder.RegisterCustomTask(actualTaskType, def => 
            {
                var task = new TTask();
                return task;
            });
            
            return flowBuilder;
        }

        /// <summary>
        /// Register multiple custom nodes from an assembly.
        /// </summary>
        /// <param name="flowBuilder">The FlowBuilder instance</param>
        /// <param name="assembly">The assembly to scan for custom nodes</param>
        /// <param name="nodeTypeFilter">Optional filter for node types</param>
        /// <returns>The FlowBuilder instance for chaining</returns>
        public static IFlowBuilder RegisterNodesFromAssembly(
            this IFlowBuilder flowBuilder, 
            Assembly assembly, 
            Func<Type, bool>? nodeTypeFilter = null)
        {
            var nodeTypes = assembly.GetTypes()
                .Where(t => typeof(IFlowNode).IsAssignableFrom(t) && 
                           !t.IsAbstract && 
                           !t.IsInterface &&
                           t.GetConstructor(Type.EmptyTypes) != null)
                .Where(t => nodeTypeFilter?.Invoke(t) ?? true);

            foreach (var nodeType in nodeTypes)
            {
                var nodeTypeName = GetNodeTypeName(nodeType);
                
                flowBuilder.RegisterCustomNode(nodeTypeName, def =>
                {
                    return (IFlowNode)Activator.CreateInstance(nodeType)!;
                });
            }

            return flowBuilder;
        }

        /// <summary>
        /// Register multiple custom tasks from an assembly.
        /// </summary>
        /// <param name="flowBuilder">The FlowBuilder instance</param>
        /// <param name="assembly">The assembly to scan for custom tasks</param>
        /// <param name="taskTypeFilter">Optional filter for task types</param>
        /// <returns>The FlowBuilder instance for chaining</returns>
        public static IFlowBuilder RegisterTasksFromAssembly(
            this IFlowBuilder flowBuilder, 
            Assembly assembly, 
            Func<Type, bool>? taskTypeFilter = null)
        {
            var taskTypes = assembly.GetTypes()
                .Where(t => typeof(IFlowTask).IsAssignableFrom(t) && 
                           !t.IsAbstract && 
                           !t.IsInterface &&
                           t.GetConstructor(Type.EmptyTypes) != null)
                .Where(t => taskTypeFilter?.Invoke(t) ?? true);

            foreach (var taskType in taskTypes)
            {
                var taskTypeName = GetTaskTypeName(taskType);
                
                flowBuilder.RegisterCustomTask(taskTypeName, def =>
                {
                    return (IFlowTask)Activator.CreateInstance(taskType)!;
                });
            }

            return flowBuilder;
        }

        /// <summary>
        /// Register custom nodes and tasks from the current assembly.
        /// </summary>
        /// <param name="flowBuilder">The FlowBuilder instance</param>
        /// <returns>The FlowBuilder instance for chaining</returns>
        public static IFlowBuilder RegisterCustomTypesFromCurrentAssembly(this IFlowBuilder flowBuilder)
        {
            var assembly = Assembly.GetCallingAssembly();
            return flowBuilder
                .RegisterNodesFromAssembly(assembly)
                .RegisterTasksFromAssembly(assembly);
        }

        /// <summary>
        /// Validate that a node type implements IFlowNode properly.
        /// </summary>
        private static void ValidateNodeType<TNode>() where TNode : class, IFlowNode, new()
        {
            var nodeType = typeof(TNode);
            
            // Check for parameterless constructor
            if (nodeType.GetConstructor(Type.EmptyTypes) == null)
            {
                throw new InvalidOperationException($"Node type {nodeType.Name} must have a parameterless constructor");
            }

            // Check for required interface implementation
            if (!typeof(IFlowNode).IsAssignableFrom(nodeType))
            {
                throw new InvalidOperationException($"Node type {nodeType.Name} must implement IFlowNode interface");
            }

            // Try to create an instance to validate it works
            try
            {
                var instance = new TNode();
                if (instance == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of node type {nodeType.Name}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to validate node type {nodeType.Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Validate that a task type implements IFlowTask properly.
        /// </summary>
        private static void ValidateTaskType<TTask>() where TTask : class, IFlowTask, new()
        {
            var taskType = typeof(TTask);
            
            // Check for parameterless constructor
            if (taskType.GetConstructor(Type.EmptyTypes) == null)
            {
                throw new InvalidOperationException($"Task type {taskType.Name} must have a parameterless constructor");
            }

            // Check for required interface implementation
            if (!typeof(IFlowTask).IsAssignableFrom(taskType))
            {
                throw new InvalidOperationException($"Task type {taskType.Name} must implement IFlowTask interface");
            }

            // Try to create an instance to validate it works
            try
            {
                var instance = new TTask();
                if (instance == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of task type {taskType.Name}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to validate task type {taskType.Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get the node type name from a Type, checking for custom attributes.
        /// </summary>
        private static string GetNodeTypeName(Type nodeType)
        {
            // Check for custom attribute first
            var attribute = nodeType.GetCustomAttribute<FlowNodeTypeAttribute>();
            if (attribute != null && !string.IsNullOrEmpty(attribute.NodeType))
            {
                return attribute.NodeType;
            }

            // Default to class name
            return nodeType.Name;
        }

        /// <summary>
        /// Get the task type name from a Type, checking for custom attributes.
        /// </summary>
        private static string GetTaskTypeName(Type taskType)
        {
            // Check for custom attribute first
            var attribute = taskType.GetCustomAttribute<FlowTaskTypeAttribute>();
            if (attribute != null && !string.IsNullOrEmpty(attribute.TaskType))
            {
                return attribute.TaskType;
            }

            // Default to class name
            return taskType.Name;
        }
    }

    /// <summary>
    /// Attribute to specify custom node type name.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class FlowNodeTypeAttribute : Attribute
    {
        public string NodeType { get; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public bool IsVisible { get; set; } = true;

        public FlowNodeTypeAttribute(string nodeType)
        {
            NodeType = nodeType;
        }
    }

    /// <summary>
    /// Attribute to specify custom task type name.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class FlowTaskTypeAttribute : Attribute
    {
        public string TaskType { get; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool IsLongRunning { get; set; } = false;
        public bool SupportsStreaming { get; set; } = false;

        public FlowTaskTypeAttribute(string taskType)
        {
            TaskType = taskType;
        }
    }

    /// <summary>
    /// Interface for custom node metadata discovery.
    /// </summary>
    public interface ICustomNodeMetadata
    {
        string NodeType { get; }
        string Name { get; }
        string Description { get; }
        string Category { get; }
        bool IsVisible { get; }
        Dictionary<string, object> GetConfigurationSchema();
    }

    /// <summary>
    /// Interface for custom task metadata discovery.
    /// </summary>
    public interface ICustomTaskMetadata
    {
        string TaskType { get; }
        string Name { get; }
        string Description { get; }
        string Category { get; }
        bool IsVisible { get; }
        bool IsLongRunning { get; }
        bool SupportsStreaming { get; }
        Dictionary<string, object> GetConfigurationSchema();
    }
}
