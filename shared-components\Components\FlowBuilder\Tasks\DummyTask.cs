using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Tasks
{
    /// <summary>
    /// Dummy Task - Simple demonstration task for testing and prototyping.
    /// Configuration: message (string) - custom message content.
    /// </summary>
    public class DummyTask : BaseFlowTask
    {
        private DummyTaskDefinition? _taskConfig;

        public DummyTask()
        {
            TaskType = "DummyTask";
            IsLongRunning = false;
            SupportsStreaming = true; // For demonstration purposes
        }

        protected override async Task<bool> InitializeTaskSpecificAsync(FlowTaskDefinition definition)
        {
            try
            {
                _taskConfig = new DummyTaskDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Configuration = definition.Configuration,
                    Metadata = definition.Metadata,
                    Message = definition.GetConfigValue<string>("message", "Default dummy task message")
                };

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateTaskSpecificAsync(FlowValidationContext context, TaskValidationResult result)
        {
            await Task.CompletedTask;

            if (_taskConfig == null)
            {
                result.Errors.Add("Task configuration is not initialized");
                return;
            }

            // Validate message
            if (string.IsNullOrEmpty(_taskConfig.Message))
            {
                result.Warnings.Add("Dummy task has no message configured");
            }

            if (_taskConfig.Message.Length > 1000)
            {
                result.Warnings.Add("Dummy task message is very long");
            }
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_taskConfig == null)
                {
                    return CreateFailureResult("Task configuration is not initialized");
                }

                // Fire stream result for demonstration
                FireStreamResult("message", $"Starting dummy task: {Name}");

                // Simulate some work with a small delay
                await Task.Delay(100, cancellationToken);

                // Process the message (could include variable substitution)
                var processedMessage = ProcessMessage(_taskConfig.Message, context);

                // Fire another stream result
                FireStreamResult("progress", new { step = "processing", message = processedMessage });

                // Simulate more work
                await Task.Delay(50, cancellationToken);

                // Prepare output data
                var outputData = new Dictionary<string, object>
                {
                    ["message"] = processedMessage,
                    ["originalMessage"] = _taskConfig.Message,
                    ["executionTime"] = (DateTime.UtcNow - startTime).TotalMilliseconds,
                    ["taskType"] = TaskType,
                    ["taskId"] = TaskId
                };

                // Set some variable fields for demonstration
                context.SetFieldValue($"dummy.{TaskId}.message", processedMessage);
                context.SetFieldValue($"dummy.{TaskId}.executedAt", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));

                // Fire completion stream result
                FireStreamResult("completed", new { message = "Dummy task completed successfully", outputData });

                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata
                result.Metadata["processedMessage"] = processedMessage;
                result.Metadata["demonstrationTask"] = true;

                return result;
            }
            catch (OperationCanceledException)
            {
                FireStreamResult("cancelled", "Dummy task was cancelled");
                return new TaskExecutionResult
                {
                    TaskId = TaskId,
                    Status = TaskExecutionStatus.Cancelled,
                    CompletedAt = DateTime.UtcNow,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
            catch (Exception ex)
            {
                FireStreamResult("error", new { error = ex.Message });
                return CreateFailureResult($"Error executing dummy task: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Process the message with variable substitution.
        /// </summary>
        private string ProcessMessage(string message, FlowExecutionContext context)
        {
            var processedMessage = message;

            // Simple variable substitution - replace {fieldName} with field values
            foreach (var field in context.InputFields)
            {
                var placeholder = $"{{{field.Key}}}";
                if (processedMessage.Contains(placeholder))
                {
                    processedMessage = processedMessage.Replace(placeholder, field.Value?.ToString() ?? "");
                }
            }

            foreach (var field in context.VariableFields)
            {
                var placeholder = $"{{{field.Key}}}";
                if (processedMessage.Contains(placeholder))
                {
                    processedMessage = processedMessage.Replace(placeholder, field.Value?.ToString() ?? "");
                }
            }

            // Replace common placeholders
            processedMessage = processedMessage.Replace("{timestamp}", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));
            processedMessage = processedMessage.Replace("{sessionId}", context.SessionId);
            processedMessage = processedMessage.Replace("{flowId}", context.FlowId);
            processedMessage = processedMessage.Replace("{taskId}", TaskId);

            return processedMessage;
        }

        /// <summary>
        /// Gets the configured message.
        /// </summary>
        public string GetMessage()
        {
            return _taskConfig?.Message ?? "";
        }
    }
}
