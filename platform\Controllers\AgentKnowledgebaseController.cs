﻿using Amazon.BedrockAgent;
using Amazon.BedrockAgent.Model;
using Amazon.DynamoDBv2.DataModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using platform.Constants;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using platform.Models.Request;
using platform.Models.Response;
using shared.Components.ApiEventBus;
using shared.Controllers;

namespace platform.Controllers
{
    [Route(Routes.AgentKnowledgebaseController.BasePath)]
    [Produces("application/json")]
    public class AgentKnowledgebaseController : SuperController
    {
        private readonly IAmazonBedrockAgent bedrockAgent;

        public AgentKnowledgebaseController(IApiEventBus eventBus, IDynamoDBContext dBContext, IAmazonBedrockAgent bedrockAgent) : base(eventBus, dBContext)
        {
            this.bedrockAgent = bedrockAgent;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Routes.AgentKnowledgebaseController.Public.ASSOC_EXISTS)]
        public async Task<IActionResult> Exists([FromQuery] string kbId, [FromQuery] string agentId)
        {
            string accountId = GetAccountId();
            var akb = await GetDBEntry<shared.Models.Documents.DynamoDB.AgentKnowledgeBase>(agentId, kbId);
            if (akb == null) return Ok(new AgentKnowledgebaseExistsResponse() { Exists = false});

            return Ok(new AgentKnowledgebaseExistsResponse() { Exists = (akb.AccountId == accountId) });
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentKnowledgebaseController.Public.ASSIGN)]
        public async Task<IActionResult> AssignAgentKnowledgeBase([FromBody] AgentAssignKbRequest request)
        {

            string accountId = GetAccountId();

            var akb = await GetDBEntry<shared.Models.Documents.DynamoDB.AgentKnowledgeBase>(request.AgentId, request.KnowledgebaseId);
            if (akb != null && akb.AccountId == accountId) return BadRequest("This association already exists.");            

            var knowledgeBase = await GetDBEntry<AwsKnowledgeBase>(accountId, request.KnowledgebaseId);
            var agent = await GetDBEntry<AwsAgent>(accountId, request.AgentId);

            if (knowledgeBase == null || agent == null)
            {
                return BadRequest("Invalid Agent or Knowledgebase.");
            }

            var awsRequest = new AssociateAgentKnowledgeBaseRequest();
            awsRequest.AgentId = agent.AwsData.AgentId;
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            awsRequest.Description = request.Description;
            awsRequest.KnowledgeBaseState = "ENABLED";
            awsRequest.AgentVersion = "DRAFT";
            var resp = await bedrockAgent.AssociateAgentKnowledgeBaseAsync(awsRequest);

            var response = new AgentKnowledgebaseResponse() { operation = shared.Models.Enums.AgentKnowledgebaseOperation.ASSIGN, AgentId = agent.AgentId, KbId = knowledgeBase.KbId };

            return Ok(response);

        }
    }
}
