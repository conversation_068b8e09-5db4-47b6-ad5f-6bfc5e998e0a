using Amazon.DynamoDBv2.DataModel;
using shared.Converters;
using System.Text.Json.Serialization;
using shared.Models.Enums;

namespace shared.Models.Documents.DynamoDB
{

    [DynamoDBTable(nameof(AccountUser))]
    public class AccountUser : DynamoDBModel
    {
        public const string SECONDARY_INDEX_NAME = "UserIndex";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexHashKey]
        public string UserId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<UserRole>))]
        [JsonConverter(typeof(JsonEnumStringConverter<UserRole>))]
        public UserRole Role { get; set; } = UserRole.Standard;
        public long LastAccess { get; set; } = 0;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(AccountUser.AccountId);
            }
            else if (indexName == SECONDARY_INDEX_NAME)
            {
                // UserIndex hash key
                return nameof(AccountUser.UserId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(AccountUser.UserId);
            }
            else if (indexName == SECONDARY_INDEX_NAME)
            {
                // UserIndex range key
                return nameof(AccountUser.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AccountUser);
        }
    }
}
