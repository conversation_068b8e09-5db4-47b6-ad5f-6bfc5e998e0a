﻿using System.Text.Json.Serialization;
using System.Xml.Linq;

namespace shared.Components.BehaviorTreeBase.Models
{
    public class TreeState
    {
        public Dictionary<string, NodeData> Nodes { get; set; } = new Dictionary<string, NodeData>();
        public Dictionary<string, string> Variables { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> Inputs { get; set; } = new Dictionary<string, string>();

        public NodeData GetNodeData(string nodeId)
        {
            if(!Nodes.ContainsKey(nodeId)) Nodes[nodeId] = new NodeData();
            return Nodes[nodeId];
        }
    }
}
