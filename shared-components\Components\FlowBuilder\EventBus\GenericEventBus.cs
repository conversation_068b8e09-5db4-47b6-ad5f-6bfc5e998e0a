using shared.Components.GenericEventBus.Interfaces;
using shared.Components.GenericEventBus.Models;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace shared.Components.FlowBuilder.EventBus
{
    /// <summary>
    /// Generic event bus implementation for handling flow execution events and message queuing.
    /// This is separate from APIEventBus and designed for internal flow processing.
    /// </summary>
    public class GenericEventBus : IGenericEventBus, IDisposable
    {
        private readonly ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>> _messageHandlers;
        private readonly ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>> _queueHandlers;
        private readonly ConcurrentDictionary<string, Channel<GenericEventMessage>> _queues;
        private readonly ConcurrentDictionary<string, MessageStatus> _messageStatuses;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _processingTask;
        private readonly SemaphoreSlim _processingLock;

        public event EventHandler<MessageProcessedEventArgs>? OnMessageProcessed;
        public event EventHandler<MessageFailedEventArgs>? OnMessageFailed;

        public GenericEventBus()
        {
            _messageHandlers = new ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>>();
            _queueHandlers = new ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>>();
            _queues = new ConcurrentDictionary<string, Channel<GenericEventMessage>>();
            _messageStatuses = new ConcurrentDictionary<string, MessageStatus>();
            _cancellationTokenSource = new CancellationTokenSource();
            _processingLock = new SemaphoreSlim(1, 1);

            // Create default queue
            CreateQueue("default");

            // Start background processing
            _processingTask = Task.Run(ProcessMessagesAsync);
        }

        public async Task<string> SendMessageAsync(GenericEventMessage message, int delayInSeconds = 0)
        {
            if (string.IsNullOrEmpty(message.Id))
            {
                message.Id = Guid.NewGuid().ToString();
            }

            // Set processing time if delay is specified
            if (delayInSeconds > 0)
            {
                message.ProcessAt = DateTime.UtcNow.AddSeconds(delayInSeconds);
            }

            // Update message status
            _messageStatuses[message.Id] = MessageStatus.Pending;

            // Send to default queue
            await SendToQueueAsync("default", message, 0);

            return message.Id;
        }

        public async Task<string> SendToQueueAsync(string queueName, GenericEventMessage message, int delayInSeconds = 0)
        {
            if (string.IsNullOrEmpty(message.Id))
            {
                message.Id = Guid.NewGuid().ToString();
            }

            // Set processing time if delay is specified
            if (delayInSeconds > 0)
            {
                message.ProcessAt = DateTime.UtcNow.AddSeconds(delayInSeconds);
            }

            // Ensure queue exists
            if (!_queues.ContainsKey(queueName))
            {
                CreateQueue(queueName);
            }

            // Update message status
            _messageStatuses[message.Id] = MessageStatus.Pending;

            // Add to queue
            var queue = _queues[queueName];
            await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);

            return message.Id;
        }

        public void Subscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler)
        {
            _messageHandlers.AddOrUpdate(messageType,
                new List<Func<GenericEventMessage, Task<bool>>> { handler },
                (key, existing) =>
                {
                    existing.Add(handler);
                    return existing;
                });
        }

        public void SubscribeToQueue(string queueName, Func<GenericEventMessage, Task<bool>> handler)
        {
            _queueHandlers.AddOrUpdate(queueName,
                new List<Func<GenericEventMessage, Task<bool>>> { handler },
                (key, existing) =>
                {
                    existing.Add(handler);
                    return existing;
                });
        }

        public void Unsubscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler)
        {
            if (_messageHandlers.TryGetValue(messageType, out var handlers))
            {
                handlers.Remove(handler);
                if (handlers.Count == 0)
                {
                    _messageHandlers.TryRemove(messageType, out _);
                }
            }
        }

        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            // Already started in constructor
            await Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            _cancellationTokenSource.Cancel();
            
            // Close all queue writers
            foreach (var queue in _queues.Values)
            {
                queue.Writer.Complete();
            }

            try
            {
                await _processingTask;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        public async Task<MessageStatus> GetMessageStatusAsync(string messageId)
        {
            await Task.CompletedTask;
            return _messageStatuses.GetValueOrDefault(messageId, MessageStatus.Pending);
        }

        /// <summary>
        /// Background task for processing messages from all queues.
        /// </summary>
        private async Task ProcessMessagesAsync()
        {
            var tasks = new List<Task>();

            try
            {
                // Start processing tasks for each queue
                foreach (var queueKvp in _queues)
                {
                    var queueName = queueKvp.Key;
                    var queue = queueKvp.Value;
                    
                    tasks.Add(ProcessQueueAsync(queueName, queue));
                }

                // Wait for all processing tasks to complete
                await Task.WhenAll(tasks);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        /// <summary>
        /// Process messages from a specific queue.
        /// </summary>
        private async Task ProcessQueueAsync(string queueName, Channel<GenericEventMessage> queue)
        {
            try
            {
                await foreach (var message in queue.Reader.ReadAllAsync(_cancellationTokenSource.Token))
                {
                    // Check if message should be delayed
                    if (message.ProcessAt.HasValue && message.ProcessAt.Value > DateTime.UtcNow)
                    {
                        // Re-queue the message with delay
                        var delay = message.ProcessAt.Value - DateTime.UtcNow;
                        _ = Task.Delay(delay, _cancellationTokenSource.Token)
                            .ContinueWith(async _ =>
                            {
                                if (!_cancellationTokenSource.Token.IsCancellationRequested)
                                {
                                    await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);
                                }
                            }, _cancellationTokenSource.Token);
                        continue;
                    }

                    await ProcessMessageAsync(queueName, message);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        /// <summary>
        /// Process a single message.
        /// </summary>
        private async Task ProcessMessageAsync(string queueName, GenericEventMessage message)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _messageStatuses[message.Id] = MessageStatus.Processing;

                var processed = false;

                // Try queue-specific handlers first
                if (_queueHandlers.TryGetValue(queueName, out var queueHandlers))
                {
                    foreach (var handler in queueHandlers.ToList())
                    {
                        try
                        {
                            if (await handler(message))
                            {
                                processed = true;
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            await HandleMessageFailure(message, ex, startTime);
                            return;
                        }
                    }
                }

                // Try message type handlers if not processed by queue handlers
                if (!processed && _messageHandlers.TryGetValue(message.MessageType, out var messageHandlers))
                {
                    foreach (var handler in messageHandlers.ToList())
                    {
                        try
                        {
                            if (await handler(message))
                            {
                                processed = true;
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            await HandleMessageFailure(message, ex, startTime);
                            return;
                        }
                    }
                }

                if (processed)
                {
                    _messageStatuses[message.Id] = MessageStatus.Completed;
                    
                    OnMessageProcessed?.Invoke(this, new MessageProcessedEventArgs
                    {
                        MessageId = message.Id,
                        MessageType = message.MessageType,
                        ProcessingTime = DateTime.UtcNow - startTime
                    });
                }
                else
                {
                    // No handler processed the message
                    await HandleMessageFailure(message, new InvalidOperationException("No handler processed the message"), startTime);
                }
            }
            catch (Exception ex)
            {
                await HandleMessageFailure(message, ex, startTime);
            }
        }

        /// <summary>
        /// Handle message processing failure.
        /// </summary>
        private async Task HandleMessageFailure(GenericEventMessage message, Exception exception, DateTime startTime)
        {
            message.RetryCount++;
            
            var willRetry = message.RetryCount < message.MaxRetries;
            
            if (willRetry)
            {
                _messageStatuses[message.Id] = MessageStatus.Retrying;
                
                // Re-queue with exponential backoff
                var delay = TimeSpan.FromSeconds(Math.Pow(2, message.RetryCount));
                message.ProcessAt = DateTime.UtcNow.Add(delay);
                
                // Send back to the same queue
                var queueName = "default"; // Default queue for retries
                if (_queues.TryGetValue(queueName, out var queue))
                {
                    await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);
                }
            }
            else
            {
                _messageStatuses[message.Id] = MessageStatus.Failed;
            }

            OnMessageFailed?.Invoke(this, new MessageFailedEventArgs
            {
                MessageId = message.Id,
                MessageType = message.MessageType,
                Exception = exception,
                RetryCount = message.RetryCount,
                WillRetry = willRetry
            });
        }

        /// <summary>
        /// Create a new queue.
        /// </summary>
        private void CreateQueue(string queueName)
        {
            var options = new BoundedChannelOptions(1000) // Limit queue size
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = false,
                SingleWriter = false
            };

            var channel = Channel.CreateBounded<GenericEventMessage>(options);
            _queues[queueName] = channel;
        }

        public void Dispose()
        {
            _cancellationTokenSource.Cancel();
            
            try
            {
                _processingTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (AggregateException)
            {
                // Ignore timeout exceptions during disposal
            }

            _cancellationTokenSource.Dispose();
            _processingLock.Dispose();
        }
    }
}
