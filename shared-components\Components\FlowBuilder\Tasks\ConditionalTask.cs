using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Tasks
{
    /// <summary>
    /// Conditional Task - Complex conditional logic with multiple IF/ELSE IF/ELSE branches.
    /// Configuration: clauses (array) - conditional clauses with conditions and tasks, elseTasks (array) - tasks for else condition.
    /// </summary>
    public class ConditionalTask : BaseFlowTask
    {
        private ConditionalTaskDefinition? _taskConfig;
        private readonly Dictionary<string, Func<FlowTaskDefinition, IFlowTask>> _taskFactories;

        public ConditionalTask(Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>? taskFactories = null)
        {
            TaskType = "ConditionalTask";
            IsLongRunning = false;
            SupportsStreaming = true;
            _taskFactories = taskFactories ?? new Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>();
        }

        protected override async Task<bool> InitializeTaskSpecificAsync(FlowTaskDefinition definition)
        {
            try
            {
                _taskConfig = new ConditionalTaskDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Configuration = definition.Configuration,
                    Metadata = definition.Metadata
                };

                // Extract clauses from configuration
                if (definition.Configuration.ContainsKey("clauses"))
                {
                    var clausesData = definition.Configuration["clauses"];
                    if (clausesData is List<ConditionalClause> clauses)
                    {
                        _taskConfig.Clauses = clauses;
                    }
                }

                // Extract else tasks from configuration
                if (definition.Configuration.ContainsKey("elseTasks"))
                {
                    var elseTasksData = definition.Configuration["elseTasks"];
                    if (elseTasksData is List<FlowTaskDefinition> elseTasks)
                    {
                        _taskConfig.ElseTasks = elseTasks;
                    }
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateTaskSpecificAsync(FlowValidationContext context, TaskValidationResult result)
        {
            await Task.CompletedTask;

            if (_taskConfig == null)
            {
                result.Errors.Add("Task configuration is not initialized");
                return;
            }

            // Validate clauses
            if (_taskConfig.Clauses == null || _taskConfig.Clauses.Count == 0)
            {
                result.Errors.Add("Conditional task must have at least one clause");
                return;
            }

            if (_taskConfig.Clauses.Count > 20)
            {
                result.Warnings.Add("Conditional task has many clauses, consider performance implications");
            }

            // Validate each clause
            for (int i = 0; i < _taskConfig.Clauses.Count; i++)
            {
                var clause = _taskConfig.Clauses[i];
                
                // Validate condition
                ValidateRuleGroup(clause.Condition, context, result, $"Clause {i}");

                // Validate tasks in clause
                if (clause.Tasks == null || clause.Tasks.Count == 0)
                {
                    result.Warnings.Add($"Clause {i} has no tasks configured");
                }
                else
                {
                    ValidateTaskList(clause.Tasks, result, $"Clause {i}");
                }
            }

            // Validate else tasks
            if (_taskConfig.ElseTasks != null && _taskConfig.ElseTasks.Count > 0)
            {
                ValidateTaskList(_taskConfig.ElseTasks, result, "Else");
            }
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_taskConfig == null)
                {
                    return CreateFailureResult("Task configuration is not initialized");
                }

                FireStreamResult("message", $"Starting conditional task: {Name}");

                var allOutputData = new Dictionary<string, object>();
                var executedTasks = new List<TaskExecutionResult>();
                bool anyConditionMet = false;
                int executedClauseIndex = -1;

                // Evaluate clauses in order
                for (int i = 0; i < _taskConfig.Clauses.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return new TaskExecutionResult
                        {
                            TaskId = TaskId,
                            Status = TaskExecutionStatus.Cancelled,
                            CompletedAt = DateTime.UtcNow,
                            ExecutionTime = DateTime.UtcNow - startTime
                        };
                    }

                    var clause = _taskConfig.Clauses[i];
                    
                    FireStreamResult("progress", new { step = "evaluating_condition", clauseIndex = i });

                    // Evaluate condition
                    bool conditionMet = await EvaluateRuleGroupAsync(clause.Condition, context);

                    if (conditionMet)
                    {
                        anyConditionMet = true;
                        executedClauseIndex = i;

                        FireStreamResult("progress", new { step = "condition_met", clauseIndex = i, taskCount = clause.Tasks.Count });

                        // Execute tasks in this clause
                        var clauseResults = await ExecuteTaskListAsync(clause.Tasks, context, cancellationToken, $"clause_{i}");
                        executedTasks.AddRange(clauseResults);

                        // Merge output data
                        foreach (var taskResult in clauseResults)
                        {
                            foreach (var kvp in taskResult.OutputData)
                            {
                                allOutputData[$"clause_{i}_task_{taskResult.TaskId}_{kvp.Key}"] = kvp.Value;
                            }
                        }

                        break; // Stop after first matching condition
                    }
                }

                // If no condition was met, execute else tasks
                if (!anyConditionMet && _taskConfig.ElseTasks != null && _taskConfig.ElseTasks.Count > 0)
                {
                    FireStreamResult("progress", new { step = "executing_else_tasks", taskCount = _taskConfig.ElseTasks.Count });

                    var elseResults = await ExecuteTaskListAsync(_taskConfig.ElseTasks, context, cancellationToken, "else");
                    executedTasks.AddRange(elseResults);

                    // Merge output data
                    foreach (var taskResult in elseResults)
                    {
                        foreach (var kvp in taskResult.OutputData)
                        {
                            allOutputData[$"else_task_{taskResult.TaskId}_{kvp.Key}"] = kvp.Value;
                        }
                    }
                }

                // Prepare final output data
                var outputData = new Dictionary<string, object>
                {
                    ["conditionMet"] = anyConditionMet,
                    ["executedClauseIndex"] = executedClauseIndex,
                    ["totalClauses"] = _taskConfig.Clauses.Count,
                    ["executedTaskCount"] = executedTasks.Count,
                    ["failedTaskCount"] = executedTasks.Count(t => t.Status == TaskExecutionStatus.Failed),
                    ["executedTasks"] = executedTasks.Select(t => new
                    {
                        taskId = t.TaskId,
                        status = t.Status.ToString(),
                        executionTime = t.ExecutionTime.TotalMilliseconds
                    }).ToList()
                };

                // Merge all task output data
                foreach (var kvp in allOutputData)
                {
                    outputData[kvp.Key] = kvp.Value;
                }

                // Set variable fields
                context.SetFieldValue($"conditional.{TaskId}.conditionMet", anyConditionMet);
                context.SetFieldValue($"conditional.{TaskId}.executedClauseIndex", executedClauseIndex);
                context.SetFieldValue($"conditional.{TaskId}.executedTaskCount", executedTasks.Count);

                // Determine if any task affects flow execution
                bool affectsFlowExecution = executedTasks.Any(t => t.AffectsFlowExecution);

                // Determine overall status
                bool anyTaskFailed = executedTasks.Any(t => t.Status == TaskExecutionStatus.Failed);
                TaskExecutionStatus overallStatus = anyTaskFailed ? TaskExecutionStatus.Failed : TaskExecutionStatus.Completed;

                FireStreamResult("completed", new { 
                    message = "Conditional task completed", 
                    conditionMet = anyConditionMet,
                    executedClauseIndex = executedClauseIndex,
                    executedTaskCount = executedTasks.Count
                });

                var result = new TaskExecutionResult
                {
                    TaskId = TaskId,
                    Status = overallStatus,
                    OutputData = outputData,
                    AffectsFlowExecution = affectsFlowExecution,
                    ExecutionTime = DateTime.UtcNow - startTime,
                    CompletedAt = DateTime.UtcNow
                };

                // Add metadata
                result.Metadata["conditionMet"] = anyConditionMet;
                result.Metadata["executedClauseIndex"] = executedClauseIndex;
                result.Metadata["executedTaskCount"] = executedTasks.Count;
                result.Metadata["anyTaskFailed"] = anyTaskFailed;

                return result;
            }
            catch (OperationCanceledException)
            {
                FireStreamResult("cancelled", "Conditional task was cancelled");
                return new TaskExecutionResult
                {
                    TaskId = TaskId,
                    Status = TaskExecutionStatus.Cancelled,
                    CompletedAt = DateTime.UtcNow,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
            catch (Exception ex)
            {
                FireStreamResult("error", new { error = ex.Message });
                return CreateFailureResult($"Error executing conditional task: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Execute a list of tasks.
        /// </summary>
        private async Task<List<TaskExecutionResult>> ExecuteTaskListAsync(List<FlowTaskDefinition> tasks, FlowExecutionContext context, CancellationToken cancellationToken, string prefix)
        {
            var results = new List<TaskExecutionResult>();

            foreach (var taskDef in tasks)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }

                try
                {
                    var taskResult = await ExecuteSubTaskAsync(taskDef, context, cancellationToken);
                    results.Add(taskResult);

                    FireStreamResult("task_completed", new { 
                        prefix, 
                        taskId = taskDef.Id, 
                        status = taskResult.Status.ToString() 
                    });
                }
                catch (Exception ex)
                {
                    var failedResult = new TaskExecutionResult
                    {
                        TaskId = taskDef.Id,
                        Status = TaskExecutionStatus.Failed,
                        ErrorMessage = ex.Message,
                        Exception = ex,
                        CompletedAt = DateTime.UtcNow
                    };
                    results.Add(failedResult);

                    FireStreamResult("task_failed", new { 
                        prefix, 
                        taskId = taskDef.Id, 
                        error = ex.Message 
                    });
                }
            }

            return results;
        }

        /// <summary>
        /// Execute a single sub-task.
        /// </summary>
        private async Task<TaskExecutionResult> ExecuteSubTaskAsync(FlowTaskDefinition taskDef, FlowExecutionContext context, CancellationToken cancellationToken)
        {
            if (!_taskFactories.ContainsKey(taskDef.Type))
            {
                return new TaskExecutionResult
                {
                    TaskId = taskDef.Id,
                    Status = TaskExecutionStatus.Failed,
                    ErrorMessage = $"Unsupported task type: {taskDef.Type}",
                    CompletedAt = DateTime.UtcNow
                };
            }

            var taskFactory = _taskFactories[taskDef.Type];
            var task = taskFactory(taskDef);

            if (!await task.InitializeAsync(taskDef))
            {
                return new TaskExecutionResult
                {
                    TaskId = taskDef.Id,
                    Status = TaskExecutionStatus.Failed,
                    ErrorMessage = "Task initialization failed",
                    CompletedAt = DateTime.UtcNow
                };
            }

            return await task.ExecuteAsync(context, cancellationToken);
        }

        /// <summary>
        /// Evaluate a rule group against the execution context.
        /// </summary>
        private async Task<bool> EvaluateRuleGroupAsync(FlowRuleGroup ruleGroup, FlowExecutionContext context)
        {
            var results = new List<bool>();

            // Evaluate individual rules
            foreach (var rule in ruleGroup.Rules)
            {
                var result = await EvaluateRuleAsync(rule, context);
                results.Add(result);
            }

            // Evaluate nested groups
            foreach (var group in ruleGroup.Groups)
            {
                var result = await EvaluateRuleGroupAsync(group, context);
                results.Add(result);
            }

            // Apply combinator logic
            if (ruleGroup.Combinator.ToLower() == "or")
                return results.Any(r => r);
            else // Default to "and"
                return results.All(r => r);
        }

        /// <summary>
        /// Evaluate a single rule against the execution context.
        /// </summary>
        private async Task<bool> EvaluateRuleAsync(FlowRule rule, FlowExecutionContext context)
        {
            await Task.CompletedTask;

            var fieldValue = context.GetFieldValue(rule.Field);
            if (fieldValue == null && rule.Value != null)
                return false;

            if (fieldValue == null && rule.Value == null)
                return rule.Operator == "=" || rule.Operator == "==";

            return rule.Operator.ToLower() switch
            {
                "=" or "==" => Equals(fieldValue, rule.Value),
                "!=" or "<>" => !Equals(fieldValue, rule.Value),
                "<" => CompareValues(fieldValue, rule.Value) < 0,
                ">" => CompareValues(fieldValue, rule.Value) > 0,
                "<=" => CompareValues(fieldValue, rule.Value) <= 0,
                ">=" => CompareValues(fieldValue, rule.Value) >= 0,
                "contains" => fieldValue?.ToString()?.Contains(rule.Value?.ToString() ?? "") == true,
                "startswith" => fieldValue?.ToString()?.StartsWith(rule.Value?.ToString() ?? "") == true,
                "endswith" => fieldValue?.ToString()?.EndsWith(rule.Value?.ToString() ?? "") == true,
                _ => false
            };
        }

        /// <summary>
        /// Compare two values for ordering operations.
        /// </summary>
        private int CompareValues(object? left, object? right)
        {
            if (left == null && right == null) return 0;
            if (left == null) return -1;
            if (right == null) return 1;

            if (left is IComparable leftComparable && right is IComparable rightComparable)
            {
                try
                {
                    return leftComparable.CompareTo(rightComparable);
                }
                catch
                {
                    return string.Compare(left.ToString(), right.ToString(), StringComparison.OrdinalIgnoreCase);
                }
            }

            return string.Compare(left.ToString(), right.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Validate a rule group and its nested rules.
        /// </summary>
        private void ValidateRuleGroup(FlowRuleGroup ruleGroup, FlowValidationContext context, TaskValidationResult result, string clauseName)
        {
            foreach (var rule in ruleGroup.Rules)
            {
                if (!context.AvailableFields.ContainsKey(rule.Field))
                {
                    result.Warnings.Add($"{clauseName} condition references unknown field: {rule.Field}");
                }
            }

            foreach (var nestedGroup in ruleGroup.Groups)
            {
                ValidateRuleGroup(nestedGroup, context, result, clauseName);
            }
        }

        /// <summary>
        /// Validate a list of tasks.
        /// </summary>
        private void ValidateTaskList(List<FlowTaskDefinition> tasks, TaskValidationResult result, string context)
        {
            for (int i = 0; i < tasks.Count; i++)
            {
                var task = tasks[i];
                
                if (string.IsNullOrEmpty(task.Id))
                {
                    result.Errors.Add($"{context} task {i} must have an ID");
                }

                if (string.IsNullOrEmpty(task.Type))
                {
                    result.Errors.Add($"{context} task {i} must have a type");
                }

                if (!_taskFactories.ContainsKey(task.Type))
                {
                    result.Errors.Add($"{context} task {i} has unsupported type: {task.Type}");
                }
            }
        }

        /// <summary>
        /// Register a task factory for sub-tasks.
        /// </summary>
        public void RegisterTaskFactory(string taskType, Func<FlowTaskDefinition, IFlowTask> factory)
        {
            _taskFactories[taskType] = factory;
        }

        /// <summary>
        /// Gets the configured clauses.
        /// </summary>
        public List<ConditionalClause> GetClauses()
        {
            return _taskConfig?.Clauses ?? new List<ConditionalClause>();
        }

        /// <summary>
        /// Gets the configured else tasks.
        /// </summary>
        public List<FlowTaskDefinition> GetElseTasks()
        {
            return _taskConfig?.ElseTasks ?? new List<FlowTaskDefinition>();
        }
    }
}
