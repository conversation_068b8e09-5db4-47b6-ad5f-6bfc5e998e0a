﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.FlowBuilder.Models;
using shared.Converters;

namespace shared.Models.Documents.DynamoDB
{
    /// <summary>
    /// DynamoDB document for storing flow definitions.
    /// </summary>
    [DynamoDBTable(nameof(FlowDocument))]
    public class FlowDocument : DynamoDBModel
    {
        public const string AccountIdIdIndex = "AccountId-Id-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdIdIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string FlowId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdIdIndex)]
        public string Id { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string FlowType { get; set; } = string.Empty; // e.g., "LLMBehaviorFlow", "MultiAgentFlow"
        public string Version { get; set; } = "1.0.0";
        public bool IsActive { get; set; } = true;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Flow>))]
        public Flow FlowDefinition { get; set; } = new Flow();

        // Override static methods for proper key resolution
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(FlowDocument), indexName);
        }

        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(FlowDocument), indexName);
        }

        public static new string? GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(FlowDocument));
        }

        public override string GetSearchString()
        {
            return $"{Name} {Description} {FlowType}".ToLower();
        }
    }
}
