using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using System.Collections.Concurrent;
using System.Text.Json;

namespace shared.Components.FlowBuilder.StateService
{
    /// <summary>
    /// In-memory implementation of IFlowStateService for Redis-like behavior.
    /// Suitable for development, testing, or when using an external Redis instance.
    /// </summary>
    public class InMemoryFlowStateService : IFlowStateService
    {
        private readonly ConcurrentDictionary<string, FlowExecutionState> _executionStates;
        private readonly ConcurrentDictionary<string, Dictionary<string, object>> _fieldValues;
        private readonly ConcurrentDictionary<string, NodeExecutionResult> _nodeResults;
        private readonly ConcurrentDictionary<string, TaskExecutionResult> _taskResults;
        private readonly ConcurrentDictionary<string, DateTime> _sessionTtls;
        private readonly Timer _cleanupTimer;
        private readonly object _lockObject = new object();

        public InMemoryFlowStateService()
        {
            _executionStates = new ConcurrentDictionary<string, FlowExecutionState>();
            _fieldValues = new ConcurrentDictionary<string, Dictionary<string, object>>();
            _nodeResults = new ConcurrentDictionary<string, NodeExecutionResult>();
            _taskResults = new ConcurrentDictionary<string, TaskExecutionResult>();
            _sessionTtls = new ConcurrentDictionary<string, DateTime>();

            // Start cleanup timer (runs every 5 minutes)
            _cleanupTimer = new Timer(CleanupExpiredSessions, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        public async Task<bool> StoreExecutionStateAsync(string sessionId, FlowExecutionState state, TimeSpan? ttl = null)
        {
            await Task.CompletedTask;

            try
            {
                state.LastUpdatedAt = DateTime.UtcNow;
                _executionStates[sessionId] = DeepClone(state);

                if (ttl.HasValue)
                {
                    _sessionTtls[sessionId] = DateTime.UtcNow.Add(ttl.Value);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<FlowExecutionState?> GetExecutionStateAsync(string sessionId)
        {
            await Task.CompletedTask;

            if (_executionStates.TryGetValue(sessionId, out var state))
            {
                // Check TTL
                if (_sessionTtls.TryGetValue(sessionId, out var expiry) && DateTime.UtcNow > expiry)
                {
                    // Session expired, remove it
                    await DeleteExecutionStateAsync(sessionId);
                    return null;
                }

                return DeepClone(state);
            }

            return null;
        }

        public async Task<bool> UpdateExecutionStateAsync(string sessionId, Dictionary<string, object> updates)
        {
            await Task.CompletedTask;

            if (!_executionStates.TryGetValue(sessionId, out var state))
            {
                return false;
            }

            try
            {
                // Apply updates using reflection or property mapping
                foreach (var update in updates)
                {
                    var property = typeof(FlowExecutionState).GetProperty(update.Key);
                    if (property != null && property.CanWrite)
                    {
                        var convertedValue = Convert.ChangeType(update.Value, property.PropertyType);
                        property.SetValue(state, convertedValue);
                    }
                }

                state.LastUpdatedAt = DateTime.UtcNow;
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExecutionStateAsync(string sessionId)
        {
            await Task.CompletedTask;

            var removed = _executionStates.TryRemove(sessionId, out _);
            _fieldValues.TryRemove(sessionId, out _);
            _sessionTtls.TryRemove(sessionId, out _);

            // Remove related node and task results
            var keysToRemove = new List<string>();
            foreach (var key in _nodeResults.Keys)
            {
                if (key.StartsWith($"{sessionId}_"))
                {
                    keysToRemove.Add(key);
                }
            }
            foreach (var key in keysToRemove)
            {
                _nodeResults.TryRemove(key, out _);
            }

            keysToRemove.Clear();
            foreach (var key in _taskResults.Keys)
            {
                if (key.StartsWith($"{sessionId}_"))
                {
                    keysToRemove.Add(key);
                }
            }
            foreach (var key in keysToRemove)
            {
                _taskResults.TryRemove(key, out _);
            }

            return removed;
        }

        public async Task<bool> StoreFieldValuesAsync(string sessionId, Dictionary<string, object> fields, TimeSpan? ttl = null)
        {
            await Task.CompletedTask;

            try
            {
                _fieldValues[sessionId] = new Dictionary<string, object>(fields);

                if (ttl.HasValue)
                {
                    _sessionTtls[sessionId] = DateTime.UtcNow.Add(ttl.Value);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetFieldValuesAsync(string sessionId)
        {
            await Task.CompletedTask;

            if (_fieldValues.TryGetValue(sessionId, out var fields))
            {
                // Check TTL
                if (_sessionTtls.TryGetValue(sessionId, out var expiry) && DateTime.UtcNow > expiry)
                {
                    _fieldValues.TryRemove(sessionId, out _);
                    return new Dictionary<string, object>();
                }

                return new Dictionary<string, object>(fields);
            }

            return new Dictionary<string, object>();
        }

        public async Task<bool> UpdateFieldValuesAsync(string sessionId, Dictionary<string, object> fieldUpdates)
        {
            await Task.CompletedTask;

            if (!_fieldValues.TryGetValue(sessionId, out var fields))
            {
                fields = new Dictionary<string, object>();
                _fieldValues[sessionId] = fields;
            }

            try
            {
                foreach (var update in fieldUpdates)
                {
                    fields[update.Key] = update.Value;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<object?> GetFieldValueAsync(string sessionId, string fieldName)
        {
            await Task.CompletedTask;

            if (_fieldValues.TryGetValue(sessionId, out var fields))
            {
                return fields.GetValueOrDefault(fieldName);
            }

            return null;
        }

        public async Task<bool> SetFieldValueAsync(string sessionId, string fieldName, object value)
        {
            await Task.CompletedTask;

            if (!_fieldValues.TryGetValue(sessionId, out var fields))
            {
                fields = new Dictionary<string, object>();
                _fieldValues[sessionId] = fields;
            }

            try
            {
                fields[fieldName] = value;
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> StoreNodeResultAsync(string sessionId, string nodeId, NodeExecutionResult result, TimeSpan? ttl = null)
        {
            await Task.CompletedTask;

            try
            {
                var key = $"{sessionId}_{nodeId}";
                _nodeResults[key] = DeepClone(result);

                if (ttl.HasValue)
                {
                    _sessionTtls[sessionId] = DateTime.UtcNow.Add(ttl.Value);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<NodeExecutionResult?> GetNodeResultAsync(string sessionId, string nodeId)
        {
            await Task.CompletedTask;

            var key = $"{sessionId}_{nodeId}";
            if (_nodeResults.TryGetValue(key, out var result))
            {
                return DeepClone(result);
            }

            return null;
        }

        public async Task<bool> StoreTaskResultAsync(string sessionId, string taskId, TaskExecutionResult result, TimeSpan? ttl = null)
        {
            await Task.CompletedTask;

            try
            {
                var key = $"{sessionId}_{taskId}";
                _taskResults[key] = DeepClone(result);

                if (ttl.HasValue)
                {
                    _sessionTtls[sessionId] = DateTime.UtcNow.Add(ttl.Value);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<TaskExecutionResult?> GetTaskResultAsync(string sessionId, string taskId)
        {
            await Task.CompletedTask;

            var key = $"{sessionId}_{taskId}";
            if (_taskResults.TryGetValue(key, out var result))
            {
                return DeepClone(result);
            }

            return null;
        }

        public async Task<IList<string>> GetActiveSessionsAsync()
        {
            await Task.CompletedTask;

            var activeSessions = new List<string>();
            var now = DateTime.UtcNow;

            foreach (var sessionId in _executionStates.Keys)
            {
                // Check if session is not expired
                if (!_sessionTtls.TryGetValue(sessionId, out var expiry) || now <= expiry)
                {
                    activeSessions.Add(sessionId);
                }
            }

            return activeSessions;
        }

        public async Task<bool> ExtendSessionTtlAsync(string sessionId, TimeSpan ttl)
        {
            await Task.CompletedTask;

            if (_executionStates.ContainsKey(sessionId))
            {
                _sessionTtls[sessionId] = DateTime.UtcNow.Add(ttl);
                return true;
            }

            return false;
        }

        public async Task<int> CleanupExpiredSessionsAsync()
        {
            await Task.CompletedTask;

            var cleanedUp = 0;
            var now = DateTime.UtcNow;
            var expiredSessions = new List<string>();

            // Find expired sessions
            foreach (var kvp in _sessionTtls)
            {
                if (now > kvp.Value)
                {
                    expiredSessions.Add(kvp.Key);
                }
            }

            // Remove expired sessions
            foreach (var sessionId in expiredSessions)
            {
                if (await DeleteExecutionStateAsync(sessionId))
                {
                    cleanedUp++;
                }
            }

            return cleanedUp;
        }

        /// <summary>
        /// Deep clone an object using JSON serialization.
        /// </summary>
        private T DeepClone<T>(T obj)
        {
            if (obj == null) return obj;

            var json = JsonSerializer.Serialize(obj);
            return JsonSerializer.Deserialize<T>(json) ?? obj;
        }

        /// <summary>
        /// Timer callback for cleaning up expired sessions.
        /// </summary>
        private async void CleanupExpiredSessions(object? state)
        {
            try
            {
                await CleanupExpiredSessionsAsync();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }
}
