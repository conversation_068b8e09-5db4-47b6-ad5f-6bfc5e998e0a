﻿using Amazon.BedrockAgentRuntime;
using Amazon.BedrockAgentRuntime.Model;
using System.Runtime.InteropServices;
using System.Text;

namespace coral_agents.Components.AWS
{
    public class Bedrock
    {
        private readonly IAmazonBedrockAgentRuntime amazonBedrockAgent;
        public Bedrock(IAmazonBedrockAgentRuntime amazonBedrockAgent) {
            this.amazonBedrockAgent = amazonBedrockAgent;
        }

        public async Task<string> InvokeAgent(string query, string agentId, string agentAlias, string agentAliasId, string sessionId, bool end_session = false)        
        {
            InvokeAgentRequest invokeAgentRequest = new InvokeAgentRequest();
            invokeAgentRequest.AgentId = agentId;
            invokeAgentRequest.SessionId = sessionId;
            invokeAgentRequest.AgentAliasId= agentAliasId;
            invokeAgentRequest.EndSession = end_session;
            invokeAgentRequest.InputText = query;

            try
            {
                var response = await amazonBedrockAgent.InvokeAgentAsync(invokeAgentRequest);
                MemoryStream output = new MemoryStream();
                foreach (PayloadPart item in response.Completion)
                {
                    item.Bytes.CopyTo(output);
                }
                var result = Encoding.UTF8.GetString(output.ToArray());
                return result;
            }
            catch (Exception ex)
            {
                Console.Write(ex.Message);
                return ex.Message;
            }
            
            
        }
    }
}
