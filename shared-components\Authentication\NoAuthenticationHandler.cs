﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace shared.Authentication
{
    public class NoAuthenticationHandler : AuthenticationHandler<NoAuthenticationOptions>
    {

        private readonly ILogger<NoAuthenticationHandler> _logger;

        public NoAuthenticationHandler(IOptionsMonitor<NoAuthenticationOptions> options, ILogger<NoAuthenticationHandler> logger, UrlEncoder encoder, ILoggerFactory loggerFactory) : base(options, loggerFactory, encoder)
        {
            _logger = logger;
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var identity = new ClaimsIdentity(
                    new[] { 
                          new Claim(ClaimTypes.Anonymous, "true"),
                          new Claim(ClaimTypes.Authentication, "false")
                    }, Scheme.Name);

            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return Task.FromResult(AuthenticateResult.Success(ticket));

        }

    }
}
