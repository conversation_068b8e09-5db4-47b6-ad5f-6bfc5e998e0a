using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.Nodes;
using shared.Components.FlowBuilder.Tasks;
using System.Collections.Concurrent;

namespace shared.Components.FlowBuilder
{
    /// <summary>
    /// Main FlowBuilder component implementation that provides flow management and execution capabilities.
    /// Supports dependency injection and multiple FlowBuilder instances.
    /// </summary>
    public class FlowBuilderComponent : IFlowBuilder
    {
        public string InstanceId { get; }
        public string InstanceName { get; }

        private readonly IFlowEngine _flowEngine;
        private readonly ConcurrentDictionary<string, Func<FlowNodeDefinition, IFlowNode>> _nodeFactories;
        private readonly ConcurrentDictionary<string, Func<FlowTaskDefinition, IFlowTask>> _taskFactories;

        public FlowBuilderComponent(
            string instanceId,
            string instanceName,
            IFlowEngine flowEngine)
        {
            InstanceId = instanceId;
            InstanceName = instanceName;
            _flowEngine = flowEngine;
            _nodeFactories = new ConcurrentDictionary<string, Func<FlowNodeDefinition, IFlowNode>>();
            _taskFactories = new ConcurrentDictionary<string, Func<FlowTaskDefinition, IFlowTask>>();

            // Register default node types
            RegisterDefaultNodeTypes();

            // Register default task types
            RegisterDefaultTaskTypes();
        }

        public async Task<FlowValidationResult> ValidateFlowAsync(Flow flow)
        {
            var result = new FlowValidationResult { IsValid = true };

            try
            {
                // Create validation context
                var context = new FlowValidationContext
                {
                    Flow = flow,
                    AvailableFields = flow.Fields.ToDictionary(f => f.Name, f => f),
                    AllNodes = flow.Nodes.ToDictionary(n => n.Id, n => n),
                    AllEdges = flow.Edges
                };

                // Validate basic flow structure
                ValidateFlowStructure(flow, result);

                // Validate nodes
                foreach (var node in flow.Nodes)
                {
                    var nodeResult = await ValidateNodeAsync(node, context);
                    result.NodeResults[node.Id] = nodeResult;
                    
                    if (!nodeResult.IsValid)
                    {
                        result.IsValid = false;
                        result.Errors.AddRange(nodeResult.Errors.Select(e => $"Node {node.Name}: {e}"));
                    }
                    
                    result.Warnings.AddRange(nodeResult.Warnings.Select(w => $"Node {node.Name}: {w}"));
                }

                // Validate edges
                ValidateEdges(flow, result);

                // Validate fields
                ValidateFields(flow, result);
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Validation error: {ex.Message}");
            }

            return result;
        }

        public async Task<FlowExecutionResult> ExecuteFlowAsync(string flowId, FlowExecutionContext executionContext, CancellationToken cancellationToken = default)
        {
            try
            {
                // This would typically load the flow from storage
                // For now, we'll assume the flow is passed in the context or loaded separately
                throw new NotImplementedException("Flow loading from storage not implemented in this example");
            }
            catch (Exception ex)
            {
                return new FlowExecutionResult
                {
                    SessionId = executionContext.SessionId,
                    FlowId = flowId,
                    Status = FlowExecutionStatus.Failed,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<string> StartFlowExecutionAsync(string flowId, FlowExecutionContext executionContext)
        {
            try
            {
                // This would typically load the flow from storage and start execution
                // For now, we'll return a session ID
                var sessionId = await _flowEngine.StartFlowExecutionSessionAsync(new Flow { Id = flowId }, executionContext);
                return sessionId;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to start flow execution: {ex.Message}", ex);
            }
        }

        public async Task<FlowExecutionStatus> GetExecutionStatusAsync(string executionSessionId)
        {
            try
            {
                var state = await _flowEngine.GetExecutionStateAsync(executionSessionId);
                return state.Status;
            }
            catch
            {
                return FlowExecutionStatus.NotStarted;
            }
        }

        public async Task<bool> CancelExecutionAsync(string executionSessionId)
        {
            try
            {
                return await _flowEngine.CancelExecutionAsync(executionSessionId);
            }
            catch
            {
                return false;
            }
        }

        public void RegisterCustomNode(string nodeType, Func<FlowNodeDefinition, IFlowNode> nodeFactory)
        {
            _nodeFactories[nodeType] = nodeFactory;
        }

        public void RegisterCustomTask(string taskType, Func<FlowTaskDefinition, IFlowTask> taskFactory)
        {
            _taskFactories[taskType] = taskFactory;
        }

        public IReadOnlyDictionary<string, Func<FlowNodeDefinition, IFlowNode>> GetRegisteredNodeTypes()
        {
            return _nodeFactories.AsReadOnly();
        }

        public IReadOnlyDictionary<string, Func<FlowTaskDefinition, IFlowTask>> GetRegisteredTaskTypes()
        {
            return _taskFactories.AsReadOnly();
        }

        /// <summary>
        /// Register default node types.
        /// </summary>
        private void RegisterDefaultNodeTypes()
        {
            _nodeFactories["SetAgent"] = def => new SetAgentNode();
            _nodeFactories["Sequence"] = def => new SequenceNode();
            _nodeFactories["Parallel"] = def => new ParallelNode();
            _nodeFactories["Select"] = def => new SelectNode();
            _nodeFactories["Loop"] = def => new LoopNode();
            _nodeFactories["Switch"] = def => new SwitchNode();
            _nodeFactories["Task"] = def => new TaskNode(_taskFactories.ToDictionary(kvp => kvp.Key, kvp => kvp.Value));
        }

        /// <summary>
        /// Register default task types.
        /// </summary>
        private void RegisterDefaultTaskTypes()
        {
            _taskFactories["DummyTask"] = def => new DummyTask();
            _taskFactories["ConditionalTask"] = def => new ConditionalTask(_taskFactories.ToDictionary(kvp => kvp.Key, kvp => kvp.Value));
            _taskFactories["LambdaTask"] = def => new LambdaTask();
        }

        /// <summary>
        /// Validate basic flow structure.
        /// </summary>
        private void ValidateFlowStructure(Flow flow, FlowValidationResult result)
        {
            if (string.IsNullOrEmpty(flow.Id))
                result.Errors.Add("Flow ID is required");

            if (string.IsNullOrEmpty(flow.Name))
                result.Errors.Add("Flow name is required");

            if (flow.Nodes == null || flow.Nodes.Count == 0)
                result.Errors.Add("Flow must have at least one node");

            if (flow.Edges == null)
                result.Errors.Add("Flow edges cannot be null");

            if (flow.Fields == null)
                result.Errors.Add("Flow fields cannot be null");

            // Check for duplicate node IDs
            var nodeIds = flow.Nodes.Select(n => n.Id).ToList();
            var duplicateNodeIds = nodeIds.GroupBy(id => id).Where(g => g.Count() > 1).Select(g => g.Key);
            foreach (var duplicateId in duplicateNodeIds)
            {
                result.Errors.Add($"Duplicate node ID: {duplicateId}");
            }

            // Check for starting nodes
            var targetNodeIds = flow.Edges.Select(e => e.Target).ToHashSet();
            var startingNodes = flow.Nodes.Where(n => !targetNodeIds.Contains(n.Id)).ToList();
            if (startingNodes.Count == 0)
            {   
                result.Warnings.Add("Flow has no starting nodes (nodes with no incoming edges)");
            }
        }

        /// <summary>
        /// Validate a single node.
        /// </summary>
        private async Task<NodeValidationResult> ValidateNodeAsync(FlowNode node, FlowValidationContext context)
        {
            var result = new NodeValidationResult { IsValid = true };

            // Check if node type is supported
            if (!_nodeFactories.ContainsKey(node.Type))
            {
                result.Errors.Add($"Unsupported node type: {node.Type}");
                return result;
            }

            try
            {
                // Create node instance and validate
                var nodeFactory = _nodeFactories[node.Type];
                var nodeDefinition = ConvertToNodeDefinition(node);
                var nodeInstance = nodeFactory(nodeDefinition);

                if (!await nodeInstance.InitializeAsync(nodeDefinition))
                {
                    result.Errors.Add("Node initialization failed");
                    return result;
                }

                var nodeValidationResult = await nodeInstance.ValidateAsync(context);
                result.Errors.AddRange(nodeValidationResult.Errors);
                result.Warnings.AddRange(nodeValidationResult.Warnings);
                result.IsValid = nodeValidationResult.IsValid;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Node validation error: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// Validate flow edges.
        /// </summary>
        private void ValidateEdges(Flow flow, FlowValidationResult result)
        {
            var nodeIds = flow.Nodes.Select(n => n.Id).ToHashSet();

            foreach (var edge in flow.Edges)
            {
                if (string.IsNullOrEmpty(edge.Id))
                    result.Errors.Add("Edge ID is required");

                if (string.IsNullOrEmpty(edge.Source))
                    result.Errors.Add($"Edge {edge.Id} source is required");
                else if (!nodeIds.Contains(edge.Source))
                    result.Errors.Add($"Edge {edge.Id} references unknown source node: {edge.Source}");

                if (string.IsNullOrEmpty(edge.Target))
                    result.Errors.Add($"Edge {edge.Id} target is required");
                else if (!nodeIds.Contains(edge.Target))
                    result.Errors.Add($"Edge {edge.Id} references unknown target node: {edge.Target}");

                if (edge.Source == edge.Target)
                    result.Warnings.Add($"Edge {edge.Id} creates a self-loop");
            }
        }

        /// <summary>
        /// Validate flow fields.
        /// </summary>
        private void ValidateFields(Flow flow, FlowValidationResult result)
        {
            var fieldNames = flow.Fields.Select(f => f.Name).ToList();
            var duplicateFieldNames = fieldNames.GroupBy(name => name).Where(g => g.Count() > 1).Select(g => g.Key);
            
            foreach (var duplicateName in duplicateFieldNames)
            {
                result.Errors.Add($"Duplicate field name: {duplicateName}");
            }

            foreach (var field in flow.Fields)
            {
                if (string.IsNullOrEmpty(field.Id))
                    result.Errors.Add("Field ID is required");

                if (string.IsNullOrEmpty(field.Name))
                    result.Errors.Add($"Field {field.Id} name is required");
            }
        }

        /// <summary>
        /// Convert FlowNode to FlowNodeDefinition.
        /// </summary>
        private FlowNodeDefinition ConvertToNodeDefinition(FlowNode node)
        {
            return new FlowNodeDefinition
            {
                Id = node.Id,
                Type = node.Type,
                Name = node.Name,
                Position = node.Position,
                Configuration = node.Data.NodeData,
                Conditions = node.Data.BaseData.NodeConditions,
                Metadata = node.Metadata
            };
        }
    }
}
