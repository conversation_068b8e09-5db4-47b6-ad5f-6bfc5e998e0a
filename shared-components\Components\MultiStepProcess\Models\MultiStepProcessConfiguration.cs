using shared.Components.MultiStepProcess.Enums;
using System.Text.Json.Serialization;
using shared.Converters;

namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Configuration settings for a multi-step process.
    /// Controls retry behavior, timeout handling, and other process-wide settings.
    /// </summary>
    public class MultiStepProcessConfiguration
    {
        /// <summary>
        /// Maximum number of retries allowed for any single step.
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// How to handle timeout failures - treat as retry or abort.
        /// </summary>
        [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessTaskRunResult>))]
        public MultiStepProcessTaskRunResult TimeoutBehavior { get; set; } = MultiStepProcessTaskRunResult.FailedRetry;

        /// <summary>
        /// Default timeout for tasks that don't specify their own timeout.
        /// Null means no default timeout.
        /// </summary>
        public TimeSpan? DefaultTaskTimeout { get; set; } = null;

        /// <summary>
        /// Whether to automatically reset retry count when transitioning to a new state.
        /// </summary>
        public bool ResetRetryCountOnStateTransition { get; set; } = true;

        /// <summary>
        /// Whether to validate the process configuration on initialization.
        /// </summary>
        public bool ValidateConfigurationOnInit { get; set; } = true;

        /// <summary>
        /// Creates a new configuration with default values.
        /// </summary>
        public MultiStepProcessConfiguration()
        {
        }

        /// <summary>
        /// Creates a new configuration with specified values.
        /// </summary>
        /// <param name="maxRetries">Maximum retries per step</param>
        /// <param name="timeoutBehavior">How to handle timeouts</param>
        /// <param name="defaultTaskTimeout">Default timeout for tasks</param>
        public MultiStepProcessConfiguration(
            int maxRetries = 3,
            MultiStepProcessTaskRunResult timeoutBehavior = MultiStepProcessTaskRunResult.FailedRetry,
            TimeSpan? defaultTaskTimeout = null)
        {
            MaxRetries = maxRetries;
            TimeoutBehavior = timeoutBehavior;
            DefaultTaskTimeout = defaultTaskTimeout;
        }

        /// <summary>
        /// Validates the configuration settings.
        /// </summary>
        /// <returns>True if configuration is valid</returns>
        /// <exception cref="ArgumentException">Thrown when configuration is invalid</exception>
        public bool Validate()
        {
            if (MaxRetries < 0)
                throw new ArgumentException("MaxRetries cannot be negative", nameof(MaxRetries));

            if (TimeoutBehavior != MultiStepProcessTaskRunResult.FailedRetry && 
                TimeoutBehavior != MultiStepProcessTaskRunResult.FailedAbort)
                throw new ArgumentException("TimeoutBehavior must be either FailedRetry or FailedAbort", nameof(TimeoutBehavior));

            if (DefaultTaskTimeout.HasValue && DefaultTaskTimeout.Value <= TimeSpan.Zero)
                throw new ArgumentException("DefaultTaskTimeout must be positive if specified", nameof(DefaultTaskTimeout));

            return true;
        }

        /// <summary>
        /// Creates a copy of this configuration.
        /// </summary>
        /// <returns>A new configuration instance with the same values</returns>
        public MultiStepProcessConfiguration Clone()
        {
            return new MultiStepProcessConfiguration
            {
                MaxRetries = MaxRetries,
                TimeoutBehavior = TimeoutBehavior,
                DefaultTaskTimeout = DefaultTaskTimeout,
                ResetRetryCountOnStateTransition = ResetRetryCountOnStateTransition,
                ValidateConfigurationOnInit = ValidateConfigurationOnInit
            };
        }
    }
}
