﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using System;
using System.Security.Claims;

namespace shared.Converters
{
    public class DynamoDBClaimConverter : IPropertyConverter
    {
        public object FromEntry(DynamoDBEntry entry)
        {
            var dentry = entry.AsDocument();
            if (dentry == null)
            {
                return new Claim("", "");
            }
            return new Claim(dentry["Type"], dentry["Value"]);
        }

        public DynamoDBEntry ToEntry(object value)
        {
            Claim c = (Claim)value;
            if (c==null)
            {
                return new Document();
            }
            return new Document(new Dictionary<string, DynamoDBEntry>() { { "Type", new Primitive(c.Type) }, { "Value", new Primitive(c.Value) } });
        }
    }
}
