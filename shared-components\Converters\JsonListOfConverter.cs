﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    public class JsonListOfConverter<T, Converter> : JsonConverter<List<T>> where Converter : JsonConverter<T>, new()
    {
        Converter converter = new Converter();

        public override List<T>? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            List<T> lst = new List<T>();
            do
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.StartArray:
                        break;
                    case JsonTokenType.EndArray:
                        return lst;
                    default:
                        T? obj = converter.Read(ref reader, typeof(T), options);
                        if(obj != null) lst.Add(obj);
                        break;
                }
            }
            while (reader.Read());
            return lst;
        }

        public override void Write(Utf8JsonWriter writer, List<T> value, JsonSerializerOptions options)
        {
            writer.WriteStartArray();
            foreach (T item in value) {
                converter.Write(writer, item, options);
            }
            writer.WriteEndArray();

        }
    }
}
