﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTreeBase.Enum;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTreeBase.Models
{
    public class NodeData
    {
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<NodeState>))]
        [JsonConverter(typeof(JsonEnumStringConverter<NodeState>))]
        public NodeState State { get; set; } = NodeState.UNKNOWN;
    }
}
