using System.Text.Json.Serialization;
using shared.Converters;

namespace shared.Components.MultiStepProcess.Enums
{
    /// <summary>
    /// Represents the reason why a multi-step process failed.
    /// Provides detailed information about failure causes for debugging and error handling.
    /// </summary>
    [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessFailureReason>))]
    public enum MultiStepProcessFailureReason
    {
        /// <summary>
        /// No failure occurred.
        /// </summary>
        None,

        /// <summary>
        /// Task execution timed out.
        /// </summary>
        Timeout,

        /// <summary>
        /// Maximum retry limit was exceeded.
        /// </summary>
        RetryLimitExceeded,

        /// <summary>
        /// Task explicitly requested abort.
        /// </summary>
        TaskAborted,

        /// <summary>
        /// Rollback process failed and cannot continue.
        /// </summary>
        RollbackFailed,

        /// <summary>
        /// Invalid state transition attempted.
        /// </summary>
        InvalidTransition,

        /// <summary>
        /// Process configuration is invalid.
        /// </summary>
        InvalidConfiguration
    }
}
