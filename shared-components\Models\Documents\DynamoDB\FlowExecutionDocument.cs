using Amazon.DynamoDBv2.DataModel;
using shared.Components.FlowBuilder.Models;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    /// <summary>
    /// DynamoDB document for storing flow execution sessions and state.
    /// </summary>
    [DynamoDBTable(nameof(FlowExecutionDocument))]
    public class FlowExecutionDocument : DynamoDBModel
    {
        public const string AccountIdSessionIdIndex = "AccountId-SessionId-index";
        public const string FlowIdStatusIndex = "FlowId-Status-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdSessionIdIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdSessionIdIndex)]
        public string SessionId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexHashKey(FlowIdStatusIndex)]
        public string FlowId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexRangeKey(FlowIdStatusIndex)]
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<FlowExecutionStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<FlowExecutionStatus>))]
        public FlowExecutionStatus Status { get; set; } = FlowExecutionStatus.NotStarted;

        public string? UserId { get; set; }
        public string FlowBuilderInstanceId { get; set; } = string.Empty;
        public string FlowBuilderInstanceName { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<FlowExecutionState>))]
        public FlowExecutionState ExecutionState { get; set; } = new();

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> InputFields { get; set; } = new();

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> VariableFields { get; set; } = new();

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> OutputFields { get; set; } = new();

        public string? ErrorMessage { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public long ExecutionTimeMs { get; set; } = 0;
        public int ExecutionStepCount { get; set; } = 0;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> ExecutionMetadata { get; set; } = new();

        // TTL for automatic cleanup (optional)
        public long? TTL { get; set; }

        // Override static methods for proper key resolution
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(FlowExecutionDocument), indexName);
        }

        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(FlowExecutionDocument), indexName);
        }

        public static new string? GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(FlowExecutionDocument));
        }

        public override string GetSearchString()
        {
            return $"{SessionId} {FlowId} {Status} {UserId}".ToLower();
        }

        /// <summary>
        /// Sets TTL for automatic cleanup after specified duration.
        /// </summary>
        public void SetTTL(TimeSpan duration)
        {
            TTL = DateTimeOffset.UtcNow.Add(duration).ToUnixTimeSeconds();
        }

        /// <summary>
        /// Converts to FlowExecutionResult.
        /// </summary>
        public FlowExecutionResult ToExecutionResult()
        {
            return new FlowExecutionResult
            {
                SessionId = SessionId,
                FlowId = FlowId,
                Status = Status,
                OutputFields = OutputFields,
                FinalFieldValues = VariableFields,
                ErrorMessage = ErrorMessage,
                ExecutionTime = TimeSpan.FromMilliseconds(ExecutionTimeMs),
                CompletedAt = CompletedAt ?? DateTime.UtcNow,
                ExecutedNodeIds = ExecutionState.CompletedNodeIds,
                ExecutionMetadata = ExecutionMetadata
            };
        }
    }

    /// <summary>
    /// DynamoDB document for storing node execution results.
    /// </summary>
    [DynamoDBTable(nameof(FlowNodeExecutionDocument))]
    public class FlowNodeExecutionDocument : DynamoDBModel
    {
        public const string SessionIdNodeIdIndex = "SessionId-NodeId-index";

        [DynamoDBHashKey]
        public string SessionId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string NodeId { get; set; } = string.Empty;

        public string FlowId { get; set; } = string.Empty;
        public string NodeType { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<NodeExecutionStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<NodeExecutionStatus>))]
        public NodeExecutionStatus Status { get; set; } = NodeExecutionStatus.NotStarted;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> OutputData { get; set; } = new();

        public string? ErrorMessage { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public long ExecutionTimeMs { get; set; } = 0;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<List<string>>))]
        public List<string> NextNodeIds { get; set; } = new();

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> Metadata { get; set; } = new();

        // TTL for automatic cleanup
        public long? TTL { get; set; }

        // Override static methods for proper key resolution
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(FlowNodeExecutionDocument), indexName);
        }

        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(FlowNodeExecutionDocument), indexName);
        }

        public static new string? GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(FlowNodeExecutionDocument));
        }

        public override string GetSearchString()
        {
            return $"{SessionId} {NodeId} {NodeType} {NodeName} {Status}".ToLower();
        }

        /// <summary>
        /// Converts to NodeExecutionResult.
        /// </summary>
        public NodeExecutionResult ToExecutionResult()
        {
            return new NodeExecutionResult
            {
                NodeId = NodeId,
                Status = Status,
                OutputData = OutputData,
                ErrorMessage = ErrorMessage,
                ExecutionTime = TimeSpan.FromMilliseconds(ExecutionTimeMs),
                CompletedAt = CompletedAt ?? DateTime.UtcNow,
                NextNodeIds = NextNodeIds,
                Metadata = Metadata
            };
        }
    }

    /// <summary>
    /// DynamoDB document for storing task execution results.
    /// </summary>
    [DynamoDBTable(nameof(FlowTaskExecutionDocument))]
    public class FlowTaskExecutionDocument : DynamoDBModel
    {
        public const string SessionIdTaskIdIndex = "SessionId-TaskId-index";

        [DynamoDBHashKey]
        public string SessionId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string TaskId { get; set; } = string.Empty;

        public string FlowId { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty;
        public string TaskName { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<TaskExecutionStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<TaskExecutionStatus>))]
        public TaskExecutionStatus Status { get; set; } = TaskExecutionStatus.NotStarted;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> OutputData { get; set; } = new();

        public string? ErrorMessage { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public long ExecutionTimeMs { get; set; } = 0;
        public bool AffectsFlowExecution { get; set; } = false;

        [DynamoDBProperty(typeof(DynamoDBJsonFallback<Dictionary<string, object>>))]
        public Dictionary<string, object> Metadata { get; set; } = new();

        // TTL for automatic cleanup
        public long? TTL { get; set; }

        // Override static methods for proper key resolution
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(FlowTaskExecutionDocument), indexName);
        }

        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(FlowTaskExecutionDocument), indexName);
        }

        public static new string? GetTableName()
        {
            return DynamoDBModel.GetTableName(typeof(FlowTaskExecutionDocument));
        }

        public override string GetSearchString()
        {
            return $"{SessionId} {TaskId} {TaskType} {TaskName} {Status}".ToLower();
        }

        /// <summary>
        /// Converts to TaskExecutionResult.
        /// </summary>
        public TaskExecutionResult ToExecutionResult()
        {
            return new TaskExecutionResult
            {
                TaskId = TaskId,
                Status = Status,
                OutputData = OutputData,
                ErrorMessage = ErrorMessage,
                ExecutionTime = TimeSpan.FromMilliseconds(ExecutionTimeMs),
                CompletedAt = CompletedAt ?? DateTime.UtcNow,
                Metadata = Metadata,
                AffectsFlowExecution = AffectsFlowExecution
            };
        }
    }
}
