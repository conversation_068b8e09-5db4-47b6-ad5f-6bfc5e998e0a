using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using shared.Components.MessageBus.Configuration;
using shared.Components.MessageBus.Interfaces;

namespace shared.Components.MessageBus.Services
{
    /// <summary>
    /// Simple message deduplication service using IMemoryCache with automatic expiration
    /// </summary>
    public class MessageDeduplicationService : IMessageDeduplicationService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<MessageDeduplicationService> _logger;
        private readonly MessageBusConfiguration _configuration;
        private readonly object _statsLock = new object();

        // Statistics
        private long _totalMessages = 0;
        private long _duplicateMessages = 0;

        public MessageDeduplicationService(
            IMemoryCache cache,
            ILogger<MessageDeduplicationService> logger,
            IOptions<MessageBusConfiguration> configuration)
        {
            _cache = cache;
            _logger = logger;
            _configuration = configuration.Value;
        }

        /// <summary>
        /// Check if a message should be processed and mark it as processing if not a duplicate
        /// </summary>
        public bool ShouldProcessMessage(string messageId)
        {
            if (!_configuration.EnableDeduplication)
                return true;

            if (string.IsNullOrWhiteSpace(messageId))
                return true;

            lock (_statsLock)
            {
                Interlocked.Increment(ref _totalMessages);

                var cacheKey = GetCacheKey(messageId);

                // Check if message is already in cache (duplicate)
                if (_cache.TryGetValue(cacheKey, out _))
                {
                    _logger.LogInformation("Message {MessageId} is a duplicate, skipping processing", messageId);
                    Interlocked.Increment(ref _duplicateMessages);
                    return false;
                }

                // Add to cache with expiration to mark as processing
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_configuration.MaximumDeduplicationPeriodMinutes),
                    Priority = CacheItemPriority.Normal
                };

                _cache.Set(cacheKey, DateTime.UtcNow, cacheOptions);

                _logger.LogDebug("Message {MessageId} marked as processing for {Minutes} minutes",
                    messageId, _configuration.MaximumDeduplicationPeriodMinutes);

                return true;
            }
        }

        /// <summary>
        /// Mark a message as requeued (removes from cache to allow reprocessing)
        /// </summary>
        public void MarkMessageRequeued(string messageId)
        {
            if (!_configuration.EnableDeduplication || string.IsNullOrWhiteSpace(messageId))
                return;

            var cacheKey = GetCacheKey(messageId);
            _cache.Remove(cacheKey);

            _logger.LogDebug("Removed message {MessageId} from cache to allow reprocessing", messageId);
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        public Dictionary<string, object> GetStatistics()
        {
            lock (_statsLock)
            {
                return new Dictionary<string, object>
                {
                    ["TotalMessages"] = _totalMessages,
                    ["DuplicateMessages"] = _duplicateMessages,
                    ["DuplicationEnabled"] = _configuration.EnableDeduplication,
                    ["MaximumDeduplicationPeriodMinutes"] = _configuration.MaximumDeduplicationPeriodMinutes
                };
            }
        }

        /// <summary>
        /// Get cache key for a message ID
        /// </summary>
        private string GetCacheKey(string messageId)
        {
            return $"msgbus_dedup_{messageId}";
        }
    }
}
