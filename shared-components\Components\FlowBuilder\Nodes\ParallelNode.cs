using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.Interfaces;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Parallel Node - Executes child nodes simultaneously.
    /// Has 1 input (input handle on left) and configurable outputs (default 2, can be increased).
    /// </summary>
    public class ParallelNode : BaseFlowNode
    {
        private ParallelNodeDefinition? _nodeConfig;

        public ParallelNode()
        {
            NodeType = "Parallel";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Multiple output handles on the right
            var outputCount = _nodeConfig?.OutputCount ?? 2;
            for (int i = 0; i < outputCount; i++)
            {
                _outputHandles.Add(new NodeHandle
                {
                    Id = $"output-{i}",
                    Name = $"Output {i + 1}",
                    Type = NodeHandleType.Output,
                    Position = "right"
                });
            }

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new ParallelNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata,
                    OutputCount = definition.GetConfigValue<int>("outputCount", 2)
                };

                // Reinitialize handles with the correct output count
                InitializeHandles();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate output count
            if (_nodeConfig.OutputCount < 1)
            {
                result.Errors.Add("Output count must be at least 1");
            }

            if (_nodeConfig.OutputCount > 20)
            {
                result.Warnings.Add("Output count is very high, consider performance implications for parallel execution");
            }

            // Validate that there are connected nodes for parallel execution
            var connectedOutputs = context.AllEdges
                .Where(e => e.Source == NodeId)
                .Select(e => e.SourceHandle)
                .Distinct()
                .Count();

            if (connectedOutputs == 0)
            {
                result.Warnings.Add("Parallel node has no connected outputs");
            }

            if (connectedOutputs == 1)
            {
                result.Warnings.Add("Parallel node has only one connected output, consider using a different node type");
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // For parallel nodes, the execution logic is primarily handled by the flow engine
                // The node itself just indicates that it's ready to proceed with parallel execution
                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "parallel",
                    ["outputCount"] = _nodeConfig.OutputCount,
                    ["parallelBranches"] = Enumerable.Range(0, _nodeConfig.OutputCount).ToList()
                };

                // Store parallel execution information in execution context
                context.ExecutionData["parallelNodeId"] = NodeId;
                context.ExecutionData["parallelOutputCount"] = _nodeConfig.OutputCount;
                context.ExecutionData["parallelExecutionId"] = Guid.NewGuid().ToString();

                // The flow engine will use this information to execute connected nodes in parallel
                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata about parallel execution
                result.Metadata["executionMode"] = "parallel";
                result.Metadata["requiresParallelExecution"] = true;
                result.Metadata["maxConcurrency"] = _nodeConfig.OutputCount;

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Parallel node: {ex.Message}", ex);
            }
        }

        public override async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            if (executionResult.Status == NodeExecutionStatus.Completed && _nodeConfig != null)
            {
                // For parallel nodes, we return all connected nodes to be executed simultaneously
                var parallelExecutionId = context.ExecutionData.GetValueOrDefault("parallelExecutionId", Guid.NewGuid().ToString());

                for (int i = 0; i < _nodeConfig.OutputCount; i++)
                {
                    var outputHandle = $"output-{i}";
                    
                    // The actual next node IDs would be determined by the flow engine
                    // based on the edges connected to this parallel node
                    if (executionResult.NextNodeIds.Count > i)
                    {
                        nextNodes.Add(new NextNodeInfo
                        {
                            NodeId = executionResult.NextNodeIds[i],
                            OutputHandle = outputHandle,
                            InputHandle = "input",
                            TransferData = new Dictionary<string, object>
                            {
                                ["parallelBranchIndex"] = i,
                                ["parallelBranchTotal"] = _nodeConfig.OutputCount,
                                ["parallelExecutionId"] = parallelExecutionId,
                                ["isParallelExecution"] = true
                            }
                        });
                    }
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// Gets the configured output count.
        /// </summary>
        public int GetOutputCount()
        {
            return _nodeConfig?.OutputCount ?? 2;
        }
    }
}
